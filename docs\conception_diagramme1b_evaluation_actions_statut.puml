@startuml Diagramme de Séquence - Évaluation des Actions et Attribution du Statut

participant "Formateur" as F
participant "«View»\nQuizDetails.jsx" as V
participant "«Controller»\nActionController" as AC
participant "«Controller»\nEvaluationController" as EC
participant "«Controller»\nQuizController" as QC
participant "«Model»\nAction" as MA
participant "«Model»\nQuiz" as MQ
participant "«Model»\nEvaluation" as ME
participant "«Model»\nCertificat" as MCE


== Évaluation des actions ==

note over F, V : QuizDetails.jsx charge automatiquement les actions\nvia QuizService.getActions() au montage du composant

F -> V : Consulter les actions du quiz (déjà chargées)
activate V

note over V : Actions déjà récupérées lors du chargement initial\nvia QuizService.getActions(token, {idmodule})

V --> F : afficher les actions avec cases à cocher
deactivate V

loop Pour chaque action
    F -> V : Cocher/décocher l'action
    activate V
    V -> AC : update(Request, id)
    activate AC
    AC -> MA : find(id)
    activate MA
    MA --> AC : action
    deactivate MA
    AC -> MA : setCompleted(checked)
    activate MA
    MA -> MA : persist()
    MA --> AC : action
    deactivate MA
    AC --> V : JsonResponse(success)
    deactivate AC
    V --> F : mettre à jour l'état en temps réel
    deactivate V
end

== Gestion MainSurface ==

alt Quiz avec MainSurface = true
    F -> V : Saisir les valeurs Main et Surface
    activate V
    V --> F : afficher les champs éditables
    F -> V : Valider les valeurs saisies
    V -> QC : update(Request, IDModule)
    activate QC
    QC -> MQ : findBy(['IDModule' => IDModule])
    activate MQ
    MQ --> QC : quizzes
    deactivate MQ
    QC -> MQ : setMain(mainValue)
    activate MQ
    QC -> MQ : setSurface(surfaceValue)
    MQ -> MQ : persist()
    MQ --> QC : quiz
    deactivate MQ
    QC --> V : JsonResponse(success)
    deactivate QC
    deactivate V

else Quiz avec MainSurface = false
    V --> F : afficher l'interface sans champs Main/Surface
end

== Attribution du statut final ==

note over F, MCE : L'évaluation finale utilise EvaluationController::create()\navec vérification automatique de la progression et génération de certificat

F -> V : Vérifier la complétude de l'évaluation
activate V

note over V : Conditions pour "Satisfaisant":\n- Toutes les compétences évaluées et acquises\n- Toutes les actions cochées\n- Main/Surface corrects (si MainSurface = true)

note over V : Conditions pour "Non Satisfaisant":\n- Toutes les compétences évaluées ET\n- Au moins une condition non remplie

V --> F : afficher état des compétences et actions

alt Toutes conditions remplies pour "Satisfaisant"
    F -> V : Attribuer statut "Satisfaisant"
    V -> EC : create(evaluationData)
    activate EC
    EC -> ME : new Evaluation()
    activate ME
    EC -> ME : setStatutEvaluation("Satisfaisant")
    EC -> ME : setQuiz(quiz)
    EC -> ME : setFormateur(formateur)
    EC -> ME : setApprenant(apprenant)
    EC -> ME : persist()
    EC -> ME : flush()
    ME --> EC : evaluation
    deactivate ME

    note over EC : checkAndGenerateCertificateIfNeeded() vérifie automatiquement\nsi tous les quiz du cours sont "Satisfaisant"

    alt Progression du cours = 100% (tous quiz "Satisfaisant")
        EC -> EC : checkAndGenerateCertificateIfNeeded()
        note over EC : Vérifie si certificat existe déjà\nou retourne course_completed = true

    else Progression du cours < 100%
        note over EC : Pas de certificat généré\nProgression mise à jour
    end

    EC --> V : JsonResponse(evaluation, certificate_info)
    deactivate EC
    V --> F : confirmer l'attribution du statut

else Conditions non remplies pour "Non Satisfaisant"
    F -> V : Attribuer statut "Non Satisfaisant"
    V -> EC : create(evaluationData)
    activate EC
    EC -> ME : new Evaluation()
    activate ME
    EC -> ME : setStatutEvaluation("Non Satisfaisant")
    EC -> ME : setQuiz(quiz)
    EC -> ME : setFormateur(formateur)
    EC -> ME : setApprenant(apprenant)
    EC -> ME : persist()
    EC -> ME : flush()
    ME --> EC : evaluation
    deactivate ME

    EC --> V : JsonResponse(evaluation)
    deactivate EC
    V --> F : confirmer l'attribution du statut
end

deactivate V

@enduml
