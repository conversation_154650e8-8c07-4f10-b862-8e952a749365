<?php

// This file has been auto-generated by the Symfony Routing Component.

return [
    'api_evenement_debug' => [[], ['_controller' => 'App\\Controller\\EvenementController::debug'], [], [['text', '/api/evenement/debug']], [], [], []],
    'api_evenement_by_administrateur' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::getByAdministrateur'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement/administrateur']], [], [], []],
    'api_evenement_by_date_range' => [[], ['_controller' => 'App\\Controller\\EvenementController::getByDateRange'], [], [['text', '/api/evenement/date-range']], [], [], []],
    'api_evenement_upcoming' => [[], ['_controller' => 'App\\Controller\\EvenementController::getUpcoming'], [], [['text', '/api/evenement/upcoming']], [], [], []],
    'api_genid' => [['id'], ['_controller' => 'api_platform.action.not_exposed', '_api_respond' => 'true'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/.well-known/genid']], [], [], []],
    'api_errors' => [['status'], ['_controller' => 'api_platform.action.error_page'], ['status' => '\\d+'], [['variable', '/', '\\d+', 'status', true], ['text', '/api/errors']], [], [], []],
    'api_validation_errors' => [['id'], ['_controller' => 'api_platform.action.not_exposed'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/validation_errors']], [], [], []],
    'api_entrypoint' => [['index', '_format'], ['_controller' => 'api_platform.action.entrypoint', '_format' => '', '_api_respond' => 'true', 'index' => 'index'], ['index' => 'index'], [['variable', '.', '[^/]++', '_format', true], ['variable', '/', 'index', 'index', true], ['text', '/api']], [], [], []],
    'api_doc' => [['_format'], ['_controller' => 'api_platform.action.documentation', '_format' => '', '_api_respond' => 'true'], [], [['variable', '.', '[^/]++', '_format', true], ['text', '/api/docs']], [], [], []],
    'api_jsonld_context' => [['shortName', '_format'], ['_controller' => 'api_platform.jsonld.action.context', '_format' => 'jsonld', '_api_respond' => 'true'], ['shortName' => '[^.]+', '_format' => 'jsonld'], [['variable', '.', 'jsonld', '_format', true], ['variable', '/', '[^.]+', 'shortName', true], ['text', '/api/contexts']], [], [], []],
    '_api_validation_errors_problem' => [['id'], ['_controller' => 'api_platform.action.placeholder', '_format' => null, '_stateless' => true, '_api_resource_class' => 'ApiPlatform\\Validator\\Exception\\ValidationException', '_api_operation_name' => '_api_validation_errors_problem'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/validation_errors']], [], [], []],
    '_api_validation_errors_hydra' => [['id'], ['_controller' => 'api_platform.action.placeholder', '_format' => null, '_stateless' => true, '_api_resource_class' => 'ApiPlatform\\Validator\\Exception\\ValidationException', '_api_operation_name' => '_api_validation_errors_hydra'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/validation_errors']], [], [], []],
    '_api_validation_errors_jsonapi' => [['id'], ['_controller' => 'api_platform.action.placeholder', '_format' => null, '_stateless' => true, '_api_resource_class' => 'ApiPlatform\\Validator\\Exception\\ValidationException', '_api_operation_name' => '_api_validation_errors_jsonapi'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/validation_errors']], [], [], []],
    'api_logout' => [[], ['_controller' => 'App\\Controller\\AuthController::logout'], [], [['text', '/api/logout']], [], [], []],
    '_preview_error' => [['code', '_format'], ['_controller' => 'error_controller::preview', '_format' => 'html'], ['code' => '\\d+'], [['variable', '.', '[^/]++', '_format', true], ['variable', '/', '\\d+', 'code', true], ['text', '/_error']], [], [], []],
    '_wdt' => [['token'], ['_controller' => 'web_profiler.controller.profiler::toolbarAction'], [], [['variable', '/', '[^/]++', 'token', true], ['text', '/_wdt']], [], [], []],
    '_profiler_home' => [[], ['_controller' => 'web_profiler.controller.profiler::homeAction'], [], [['text', '/_profiler/']], [], [], []],
    '_profiler_search' => [[], ['_controller' => 'web_profiler.controller.profiler::searchAction'], [], [['text', '/_profiler/search']], [], [], []],
    '_profiler_search_bar' => [[], ['_controller' => 'web_profiler.controller.profiler::searchBarAction'], [], [['text', '/_profiler/search_bar']], [], [], []],
    '_profiler_phpinfo' => [[], ['_controller' => 'web_profiler.controller.profiler::phpinfoAction'], [], [['text', '/_profiler/phpinfo']], [], [], []],
    '_profiler_xdebug' => [[], ['_controller' => 'web_profiler.controller.profiler::xdebugAction'], [], [['text', '/_profiler/xdebug']], [], [], []],
    '_profiler_font' => [['fontName'], ['_controller' => 'web_profiler.controller.profiler::fontAction'], [], [['text', '.woff2'], ['variable', '/', '[^/\\.]++', 'fontName', true], ['text', '/_profiler/font']], [], [], []],
    '_profiler_search_results' => [['token'], ['_controller' => 'web_profiler.controller.profiler::searchResultsAction'], [], [['text', '/search/results'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_open_file' => [[], ['_controller' => 'web_profiler.controller.profiler::openAction'], [], [['text', '/_profiler/open']], [], [], []],
    '_profiler' => [['token'], ['_controller' => 'web_profiler.controller.profiler::panelAction'], [], [['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_router' => [['token'], ['_controller' => 'web_profiler.controller.router::panelAction'], [], [['text', '/router'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_exception' => [['token'], ['_controller' => 'web_profiler.controller.exception_panel::body'], [], [['text', '/exception'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_exception_css' => [['token'], ['_controller' => 'web_profiler.controller.exception_panel::stylesheet'], [], [['text', '/exception.css'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    'api_action_list' => [[], ['_controller' => 'App\\Controller\\ActionController::list'], [], [['text', '/api/actions']], [], [], []],
    'api_action_show' => [['id'], ['_controller' => 'App\\Controller\\ActionController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/actions']], [], [], []],
    'api_action_create' => [[], ['_controller' => 'App\\Controller\\ActionController::create'], [], [['text', '/api/actions']], [], [], []],
    'api_actions_update' => [['id'], ['_controller' => 'App\\Controller\\ActionController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/actions']], [], [], []],
    'api_actions_delete' => [['id'], ['_controller' => 'App\\Controller\\ActionController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/actions']], [], [], []],
    'api_admin_users' => [[], ['_controller' => 'App\\Controller\\AdminController::getApprovedUsers'], [], [['text', '/api/admin/users']], [], [], []],
    'api_admin_users_pending' => [[], ['_controller' => 'App\\Controller\\AdminController::getPendingUsers'], [], [['text', '/api/admin/users/pending']], [], [], []],
    'api_admin_user_approve' => [['id'], ['_controller' => 'App\\Controller\\AdminController::approveUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/approve']], [], [], []],
    'api_admin_user_reject' => [['id'], ['_controller' => 'App\\Controller\\AdminController::rejectUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/reject']], [], [], []],
    'api_admin_user_add' => [[], ['_controller' => 'App\\Controller\\AdminController::addUser'], [], [['text', '/api/admin/users/add']], [], [], []],
    'api_admin_user_edit' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/edit']], [], [], []],
    'api_admin_user_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/delete']], [], [], []],
    'api_admin_apprenants' => [[], ['_controller' => 'App\\Controller\\AdminController::getApprenants'], [], [['text', '/api/admin/apprenants']], [], [], []],
    'api_admin_apprenant_cours' => [['id'], ['_controller' => 'App\\Controller\\AdminController::getApprenantCours'], [], [['text', '/cours'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/apprenants']], [], [], []],
    'api_admin_apprenant_assign_cours' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\AdminController::assignCourseToApprenant'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/admin/apprenants']], [], [], []],
    'api_admin_apprenant_remove_cours' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\AdminController::removeCourseFromApprenant'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/admin/apprenants']], [], [], []],
    'api_apprenant_cours' => [[], ['_controller' => 'App\\Controller\\ApprenantController::getMesCours'], [], [['text', '/api/apprenant/cours']], [], [], []],
    'api_register' => [[], ['_controller' => 'App\\Controller\\AuthController::register'], [], [['text', '/api/register']], [], [], []],
    'api_login' => [[], ['_controller' => 'App\\Controller\\AuthController::login'], [], [['text', '/api/login']], [], [], []],
    'api_user_me' => [[], ['_controller' => 'App\\Controller\\AuthController::getCurrentUser'], [], [['text', '/api/user/me']], [], [], []],
    'api_user_update' => [['id'], ['_controller' => 'App\\Controller\\AuthController::updateUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/user']], [], [], []],
    'api_user_delete' => [['id'], ['_controller' => 'App\\Controller\\AuthController::deleteUser'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/user']], [], [], []],
    'api_forgot_password' => [[], ['_controller' => 'App\\Controller\\AuthController::forgotPassword'], [], [['text', '/api/forgot-password']], [], [], []],
    'api_reset_password' => [['token'], ['_controller' => 'App\\Controller\\AuthController::resetPassword'], [], [['variable', '/', '[^/]++', 'token', true], ['text', '/api/reset-password']], [], [], []],
    'api_certificat_list' => [[], ['_controller' => 'App\\Controller\\CertificatController::list'], [], [['text', '/api/certificat']], [], [], []],
    'api_certificat_check_and_generate' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\CertificatController::checkAndGenerate'], [], [['variable', '/', '[^/]++', 'coursId', true], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/certificat/check-and-generate']], [], [], []],
    'api_certificat_show' => [['id'], ['_controller' => 'App\\Controller\\CertificatController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/certificat']], [], [], []],
    'api_certificat_generate_direct' => [[], ['_controller' => 'App\\Controller\\CertificatController::generateDirect'], [], [['text', '/api/certificat/generate-direct']], [], [], []],
    'api_certificat_generate' => [[], ['_controller' => 'App\\Controller\\CertificatController::generate'], [], [['text', '/api/certificat/generate']], [], [], []],
    'api_certificat_by_apprenant' => [['apprenantId'], ['_controller' => 'App\\Controller\\CertificatController::getCertificatsByApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/certificat/apprenant']], [], [], []],
    'api_certificat_by_apprenant_and_cours' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\CertificatController::getCertificatByApprenantAndCours'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/certificat/apprenant']], [], [], []],
    'api_certificat_download' => [['id'], ['_controller' => 'App\\Controller\\CertificatController::download'], [], [['text', '/download'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/certificat']], [], [], []],
    'api_certificat_data' => [['id'], ['_controller' => 'App\\Controller\\CertificatController::getCertificatData'], [], [['text', '/data'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/certificat']], [], [], []],
    'api_competence_list' => [[], ['_controller' => 'App\\Controller\\CompetenceController::list'], [], [['text', '/api/competence']], [], [], []],
    'api_competence_show' => [['id'], ['_controller' => 'App\\Controller\\CompetenceController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/competence']], [], [], []],
    'api_cours_index' => [[], ['_controller' => 'App\\Controller\\CourseController::index'], [], [['text', '/api/cours']], [], [], []],
    'api_cours_create' => [[], ['_controller' => 'App\\Controller\\CourseController::create'], [], [['text', '/api/cours']], [], [], []],
    'api_cours_show' => [['id'], ['_controller' => 'App\\Controller\\CourseController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/cours']], [], [], []],
    'api_cours_update' => [['id'], ['_controller' => 'App\\Controller\\CourseController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/cours']], [], [], []],
    'api_cours_delete' => [['id'], ['_controller' => 'App\\Controller\\CourseController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/cours']], [], [], []],
    'api_test_pending_users' => [[], ['_controller' => 'App\\Controller\\DashboardController::testPendingUsers'], [], [['text', '/api/dashboard/test-pending-users']], [], [], []],
    'api_dashboard_stats' => [[], ['_controller' => 'App\\Controller\\DashboardController::getStats'], [], [['text', '/api/dashboard/stats']], [], [], []],
    'api_dashboard_formateur_stats' => [[], ['_controller' => 'App\\Controller\\DashboardController::getFormateurStats'], [], [['text', '/api/dashboard/formateur/stats']], [], [], []],
    'api_diagnostic_check_apprenant' => [['id'], ['_controller' => 'App\\Controller\\DiagnosticController::checkApprenant'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/diagnostic/check-apprenant']], [], [], []],
    'api_diagnostic_check_cours' => [['id'], ['_controller' => 'App\\Controller\\DiagnosticController::checkCours'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/diagnostic/check-cours']], [], [], []],
    'api_diagnostic_check_progression' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\DiagnosticController::checkProgression'], [], [['variable', '/', '[^/]++', 'coursId', true], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/diagnostic/check-progression']], [], [], []],
    'api_diagnostic_create_progression' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\DiagnosticController::createProgression'], [], [['variable', '/', '[^/]++', 'coursId', true], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/diagnostic/create-progression']], [], [], []],
    'api_evaluation_list' => [[], ['_controller' => 'App\\Controller\\EvaluationController::list'], [], [['text', '/api/evaluation']], [], [], []],
    'api_evaluation_show' => [['id'], ['_controller' => 'App\\Controller\\EvaluationController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evaluation']], [], [], []],
    'api_evaluation_create' => [[], ['_controller' => 'App\\Controller\\EvaluationController::create'], [], [['text', '/api/evaluation']], [], [], []],
    'api_evaluation_by_idmodule' => [['idmodule'], ['_controller' => 'App\\Controller\\EvaluationController::getEvaluationsByIdmodule'], [], [['variable', '/', '[^/]++', 'idmodule', true], ['text', '/api/evaluation/idmodule']], [], [], []],
    'api_evaluation_by_idmodule_apprenant' => [['idmodule', 'apprenantId'], ['_controller' => 'App\\Controller\\EvaluationController::getEvaluationByIdmoduleAndApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'idmodule', true], ['text', '/api/evaluation/idmodule']], [], [], []],
    'api_evaluation_by_quiz_apprenant' => [['quizId', 'apprenantId'], ['_controller' => 'App\\Controller\\EvaluationController::getEvaluationByQuizAndApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'quizId', true], ['text', '/api/evaluation/quiz']], [], [], []],
    'api_evaluation_detail_create' => [[], ['_controller' => 'App\\Controller\\EvaluationDetailController::create'], [], [['text', '/api/evaluation-detail']], [], [], []],
    'api_evaluation_detail_by_quiz_apprenant' => [['quizId', 'apprenantId'], ['_controller' => 'App\\Controller\\EvaluationDetailController::getByQuizAndApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'quizId', true], ['text', '/api/evaluation-detail/quiz']], [], [], []],
    'api_evenement_list' => [[], ['_controller' => 'App\\Controller\\EvenementController::list'], [], [['text', '/api/evenement']], [], [], []],
    'api_evenement_show' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'api_evenement_create' => [[], ['_controller' => 'App\\Controller\\EvenementController::create'], [], [['text', '/api/evenement']], [], [], []],
    'api_evenement_update' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'api_evenement_delete' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'api_evenement_add_administrateur' => [['id', 'adminId'], ['_controller' => 'App\\Controller\\EvenementController::addAdministrateur'], [], [['variable', '/', '[^/]++', 'adminId', true], ['text', '/administrateur'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'api_evenement_remove_administrateur' => [['id', 'adminId'], ['_controller' => 'App\\Controller\\EvenementController::removeAdministrateur'], [], [['variable', '/', '[^/]++', 'adminId', true], ['text', '/administrateur'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'api_upload_profile_image' => [[], ['_controller' => 'App\\Controller\\FileUploadController::uploadProfileImage'], [], [['text', '/api/upload/profile-image']], [], [], []],
    'api_fix' => [[], ['_controller' => 'App\\Controller\\FixController::fix'], [], [['text', '/api/fix']], [], [], []],
    'api_formateur_apprenants' => [[], ['_controller' => 'App\\Controller\\FormateurController::getApprenants'], [], [['text', '/api/formateur/apprenants']], [], [], []],
    'api_formateur_apprenant' => [['id'], ['_controller' => 'App\\Controller\\FormateurController::getApprenant'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'api_formateur_apprenant_cours' => [['id'], ['_controller' => 'App\\Controller\\FormateurController::getApprenantCours'], [], [['text', '/cours'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'api_formateur_cours' => [[], ['_controller' => 'App\\Controller\\FormateurController::getCours'], [], [['text', '/api/formateur/cours']], [], [], []],
    'api_formateur_apprenant_cours_by_category' => [['id', 'category'], ['_controller' => 'App\\Controller\\FormateurController::getApprenantCoursByCategory'], [], [['variable', '/', '[^/]++', 'category', true], ['text', '/cours/category'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'api_formateur_apprenant_cours_all_categories' => [['id'], ['_controller' => 'App\\Controller\\FormateurController::getApprenantCoursAllCategories'], [], [['text', '/cours/all-categories'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'api_messagerie_get_conversation' => [['formateurId', 'apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::getConversation'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'api_messagerie_formateur_conversations' => [['formateurId'], ['_controller' => 'App\\Controller\\MessagerieController::getFormateurConversations'], [], [['text', '/conversations'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'api_messagerie_apprenant_conversations' => [['apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::getApprenantConversations'], [], [['text', '/conversations'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/messagerie/apprenant']], [], [], []],
    'api_messagerie_formateur_envoyer' => [['formateurId', 'apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::formateurEnvoyerMessage'], [], [['text', '/envoyer'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'api_messagerie_apprenant_envoyer' => [['apprenantId', 'formateurId'], ['_controller' => 'App\\Controller\\MessagerieController::apprenantEnvoyerMessage'], [], [['text', '/envoyer'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/formateur'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/messagerie/apprenant']], [], [], []],
    'api_messagerie_marquer_lu' => [['id'], ['_controller' => 'App\\Controller\\MessagerieController::marquerLu'], [], [['text', '/marquer-lu'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/messagerie']], [], [], []],
    'api_messagerie_apprenant_formateurs' => [['apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::getFormateursForApprenant'], [], [['text', '/formateurs'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/messagerie/apprenant']], [], [], []],
    'api_messagerie_formateur_apprenants' => [['formateurId'], ['_controller' => 'App\\Controller\\MessagerieController::getApprenantsForFormateur'], [], [['text', '/apprenants'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'api_notification_list' => [[], ['_controller' => 'App\\Controller\\NotificationController::getUserNotifications'], [], [['text', '/api/notification']], [], [], []],
    'api_notification_mark_read' => [['id'], ['_controller' => 'App\\Controller\\NotificationController::markAsRead'], [], [['text', '/read'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/notification']], [], [], []],
    'api_notification_mark_all_read' => [[], ['_controller' => 'App\\Controller\\NotificationController::markAllAsRead'], [], [['text', '/api/notification/mark-all-read']], [], [], []],
    'api_notification_delete' => [['id'], ['_controller' => 'App\\Controller\\NotificationController::deleteNotification'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/notification']], [], [], []],
    'api_progression_list' => [[], ['_controller' => 'App\\Controller\\ProgressionController::list'], [], [['text', '/api/progression']], [], [], []],
    'api_progression_show' => [['id'], ['_controller' => 'App\\Controller\\ProgressionController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/progression']], [], [], []],
    'api_progression_by_apprenant' => [['apprenantId'], ['_controller' => 'App\\Controller\\ProgressionController::getProgressionByApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/progression/apprenant']], [], [], []],
    'api_progression_by_apprenant_cours' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\ProgressionController::getProgressionByApprenantAndCours'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/progression/apprenant']], [], [], []],
    'api_quiz_create_batch' => [[], ['_controller' => 'App\\Controller\\QuizController::createBatch'], [], [['text', '/api/quiz/batch']], [], [], []],
    'api_quiz_list' => [[], ['_controller' => 'App\\Controller\\QuizController::list'], [], [['text', '/api/quiz']], [], [], []],
    'api_quiz_show' => [['IDModule'], ['_controller' => 'App\\Controller\\QuizController::show'], [], [['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz']], [], [], []],
    'api_quiz_update' => [['IDModule'], ['_controller' => 'App\\Controller\\QuizController::update'], [], [['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz']], [], [], []],
    'api_quiz_delete' => [['id'], ['_controller' => 'App\\Controller\\QuizController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/quiz']], [], [], []],
    'api_quiz_delete_by_idmodule' => [['IDModule'], ['_controller' => 'App\\Controller\\QuizController::deleteByIdModule'], [], [['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/by-idmodule']], [], [], []],
    'api_competence_delete' => [['IDModule', 'competenceId'], ['_controller' => 'App\\Controller\\QuizController::deleteCompetence'], [], [['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/competence']], [], [], []],
    'api_quiz_action_delete_legacy' => [['IDModule', 'competenceId', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::deleteAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/action']], [], [], []],
    'api_competence_update' => [['IDModule', 'competenceId'], ['_controller' => 'App\\Controller\\QuizController::updateCompetence'], [], [['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/competence']], [], [], []],
    'api_sous_competence_update_by_id' => [['IDModule', 'competenceId', 'id'], ['_controller' => 'App\\Controller\\QuizController::updateSousCompetenceById'], [], [['variable', '/', '[^/]++', 'id', true], ['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/sous-competence-by-id']], [], [], []],
    'api_quiz_action_update_legacy' => [['IDModule', 'competenceId', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::updateAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/action']], [], [], []],
    'api_competence_create' => [[], ['_controller' => 'App\\Controller\\QuizController::createCompetence'], [], [['text', '/api/quiz/competence/create']], [], [], []],
    'api_quiz_action_create_legacy' => [[], ['_controller' => 'App\\Controller\\QuizController::createAction'], [], [['text', '/api/quiz/action/create']], [], [], []],
    'api_quiz_action_create' => [[], ['_controller' => 'App\\Controller\\QuizController::createQuizAction'], [], [['text', '/api/quiz/quiz-action/create']], [], [], []],
    'api_quiz_action_delete_by_id' => [['id'], ['_controller' => 'App\\Controller\\QuizController::deleteQuizActionById'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/quiz/quiz-action-by-id']], [], [], []],
    'api_quiz_action_delete' => [['IDModule', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::deleteQuizAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/quiz-action']], [], [], []],
    'api_quiz_action_update_by_id' => [['id'], ['_controller' => 'App\\Controller\\QuizController::updateQuizActionById'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/quiz/quiz-action-by-id']], [], [], []],
    'api_quiz_action_update' => [['IDModule', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::updateQuizAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/quiz-action']], [], [], []],
    'api_reclamation_list' => [[], ['_controller' => 'App\\Controller\\ReclamationController::list'], [], [['text', '/api/reclamation']], [], [], []],
    'api_reclamation_user' => [[], ['_controller' => 'App\\Controller\\ReclamationController::getUserReclamations'], [], [['text', '/api/reclamation/user']], [], [], []],
    'api_reclamation_show' => [['id'], ['_controller' => 'App\\Controller\\ReclamationController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/reclamation']], [], [], []],
    'api_reclamation_create' => [[], ['_controller' => 'App\\Controller\\ReclamationController::create'], [], [['text', '/api/reclamation']], [], [], []],
    'api_reclamation_reply' => [['id'], ['_controller' => 'App\\Controller\\ReclamationController::reply'], [], [['text', '/reply'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/reclamation']], [], [], []],
    'api_sous_competence_list' => [[], ['_controller' => 'App\\Controller\\SousCompetenceController::list'], [], [['text', '/api/sous-competence']], [], [], []],
    'api_sous_competence_show' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
    'api_sous_competence_create' => [[], ['_controller' => 'App\\Controller\\SousCompetenceController::create'], [], [['text', '/api/sous-competence']], [], [], []],
    'api_sous_competence_update' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
    'api_sous_competence_delete' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
    'App\Controller\ActionController::list' => [[], ['_controller' => 'App\\Controller\\ActionController::list'], [], [['text', '/api/actions']], [], [], []],
    'App\Controller\ActionController::show' => [['id'], ['_controller' => 'App\\Controller\\ActionController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/actions']], [], [], []],
    'App\Controller\ActionController::create' => [[], ['_controller' => 'App\\Controller\\ActionController::create'], [], [['text', '/api/actions']], [], [], []],
    'App\Controller\ActionController::update' => [['id'], ['_controller' => 'App\\Controller\\ActionController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/actions']], [], [], []],
    'App\Controller\ActionController::delete' => [['id'], ['_controller' => 'App\\Controller\\ActionController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/actions']], [], [], []],
    'App\Controller\AdminController::getApprovedUsers' => [[], ['_controller' => 'App\\Controller\\AdminController::getApprovedUsers'], [], [['text', '/api/admin/users']], [], [], []],
    'App\Controller\AdminController::getPendingUsers' => [[], ['_controller' => 'App\\Controller\\AdminController::getPendingUsers'], [], [['text', '/api/admin/users/pending']], [], [], []],
    'App\Controller\AdminController::approveUser' => [['id'], ['_controller' => 'App\\Controller\\AdminController::approveUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/approve']], [], [], []],
    'App\Controller\AdminController::rejectUser' => [['id'], ['_controller' => 'App\\Controller\\AdminController::rejectUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/reject']], [], [], []],
    'App\Controller\AdminController::addUser' => [[], ['_controller' => 'App\\Controller\\AdminController::addUser'], [], [['text', '/api/admin/users/add']], [], [], []],
    'App\Controller\AdminController::editUser' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/edit']], [], [], []],
    'App\Controller\AdminController::deleteUser' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/users/delete']], [], [], []],
    'App\Controller\AdminController::getApprenants' => [[], ['_controller' => 'App\\Controller\\AdminController::getApprenants'], [], [['text', '/api/admin/apprenants']], [], [], []],
    'App\Controller\AdminController::getApprenantCours' => [['id'], ['_controller' => 'App\\Controller\\AdminController::getApprenantCours'], [], [['text', '/cours'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/admin/apprenants']], [], [], []],
    'App\Controller\AdminController::assignCourseToApprenant' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\AdminController::assignCourseToApprenant'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/admin/apprenants']], [], [], []],
    'App\Controller\AdminController::removeCourseFromApprenant' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\AdminController::removeCourseFromApprenant'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/admin/apprenants']], [], [], []],
    'App\Controller\ApprenantController::getMesCours' => [[], ['_controller' => 'App\\Controller\\ApprenantController::getMesCours'], [], [['text', '/api/apprenant/cours']], [], [], []],
    'App\Controller\AuthController::register' => [[], ['_controller' => 'App\\Controller\\AuthController::register'], [], [['text', '/api/register']], [], [], []],
    'App\Controller\AuthController::login' => [[], ['_controller' => 'App\\Controller\\AuthController::login'], [], [['text', '/api/login']], [], [], []],
    'App\Controller\AuthController::getCurrentUser' => [[], ['_controller' => 'App\\Controller\\AuthController::getCurrentUser'], [], [['text', '/api/user/me']], [], [], []],
    'App\Controller\AuthController::updateUser' => [['id'], ['_controller' => 'App\\Controller\\AuthController::updateUser'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/user']], [], [], []],
    'App\Controller\AuthController::deleteUser' => [['id'], ['_controller' => 'App\\Controller\\AuthController::deleteUser'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/user']], [], [], []],
    'App\Controller\AuthController::forgotPassword' => [[], ['_controller' => 'App\\Controller\\AuthController::forgotPassword'], [], [['text', '/api/forgot-password']], [], [], []],
    'App\Controller\AuthController::resetPassword' => [['token'], ['_controller' => 'App\\Controller\\AuthController::resetPassword'], [], [['variable', '/', '[^/]++', 'token', true], ['text', '/api/reset-password']], [], [], []],
    'App\Controller\CertificatController::list' => [[], ['_controller' => 'App\\Controller\\CertificatController::list'], [], [['text', '/api/certificat']], [], [], []],
    'App\Controller\CertificatController::checkAndGenerate' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\CertificatController::checkAndGenerate'], [], [['variable', '/', '[^/]++', 'coursId', true], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/certificat/check-and-generate']], [], [], []],
    'App\Controller\CertificatController::show' => [['id'], ['_controller' => 'App\\Controller\\CertificatController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/certificat']], [], [], []],
    'App\Controller\CertificatController::generateDirect' => [[], ['_controller' => 'App\\Controller\\CertificatController::generateDirect'], [], [['text', '/api/certificat/generate-direct']], [], [], []],
    'App\Controller\CertificatController::generate' => [[], ['_controller' => 'App\\Controller\\CertificatController::generate'], [], [['text', '/api/certificat/generate']], [], [], []],
    'App\Controller\CertificatController::getCertificatsByApprenant' => [['apprenantId'], ['_controller' => 'App\\Controller\\CertificatController::getCertificatsByApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/certificat/apprenant']], [], [], []],
    'App\Controller\CertificatController::getCertificatByApprenantAndCours' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\CertificatController::getCertificatByApprenantAndCours'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/certificat/apprenant']], [], [], []],
    'App\Controller\CertificatController::download' => [['id'], ['_controller' => 'App\\Controller\\CertificatController::download'], [], [['text', '/download'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/certificat']], [], [], []],
    'App\Controller\CertificatController::getCertificatData' => [['id'], ['_controller' => 'App\\Controller\\CertificatController::getCertificatData'], [], [['text', '/data'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/certificat']], [], [], []],
    'App\Controller\CompetenceController::list' => [[], ['_controller' => 'App\\Controller\\CompetenceController::list'], [], [['text', '/api/competence']], [], [], []],
    'App\Controller\CompetenceController::show' => [['id'], ['_controller' => 'App\\Controller\\CompetenceController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/competence']], [], [], []],
    'App\Controller\CompetenceController::create' => [[], ['_controller' => 'App\\Controller\\QuizController::createCompetence'], [], [['text', '/api/quiz/competence/create']], [], [], []],
    'App\Controller\CompetenceController::update' => [['IDModule', 'competenceId'], ['_controller' => 'App\\Controller\\QuizController::updateCompetence'], [], [['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/competence']], [], [], []],
    'App\Controller\CompetenceController::delete' => [['IDModule', 'competenceId'], ['_controller' => 'App\\Controller\\QuizController::deleteCompetence'], [], [['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/competence']], [], [], []],
    'App\Controller\CourseController::index' => [[], ['_controller' => 'App\\Controller\\CourseController::index'], [], [['text', '/api/cours']], [], [], []],
    'App\Controller\CourseController::create' => [[], ['_controller' => 'App\\Controller\\CourseController::create'], [], [['text', '/api/cours']], [], [], []],
    'App\Controller\CourseController::show' => [['id'], ['_controller' => 'App\\Controller\\CourseController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/cours']], [], [], []],
    'App\Controller\CourseController::update' => [['id'], ['_controller' => 'App\\Controller\\CourseController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/cours']], [], [], []],
    'App\Controller\CourseController::delete' => [['id'], ['_controller' => 'App\\Controller\\CourseController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/cours']], [], [], []],
    'App\Controller\DashboardController::testPendingUsers' => [[], ['_controller' => 'App\\Controller\\DashboardController::testPendingUsers'], [], [['text', '/api/dashboard/test-pending-users']], [], [], []],
    'App\Controller\DashboardController::getStats' => [[], ['_controller' => 'App\\Controller\\DashboardController::getStats'], [], [['text', '/api/dashboard/stats']], [], [], []],
    'App\Controller\DashboardController::getFormateurStats' => [[], ['_controller' => 'App\\Controller\\DashboardController::getFormateurStats'], [], [['text', '/api/dashboard/formateur/stats']], [], [], []],
    'App\Controller\DiagnosticController::checkApprenant' => [['id'], ['_controller' => 'App\\Controller\\DiagnosticController::checkApprenant'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/diagnostic/check-apprenant']], [], [], []],
    'App\Controller\DiagnosticController::checkCours' => [['id'], ['_controller' => 'App\\Controller\\DiagnosticController::checkCours'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/diagnostic/check-cours']], [], [], []],
    'App\Controller\DiagnosticController::checkProgression' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\DiagnosticController::checkProgression'], [], [['variable', '/', '[^/]++', 'coursId', true], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/diagnostic/check-progression']], [], [], []],
    'App\Controller\DiagnosticController::createProgression' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\DiagnosticController::createProgression'], [], [['variable', '/', '[^/]++', 'coursId', true], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/diagnostic/create-progression']], [], [], []],
    'App\Controller\EvaluationController::list' => [[], ['_controller' => 'App\\Controller\\EvaluationController::list'], [], [['text', '/api/evaluation']], [], [], []],
    'App\Controller\EvaluationController::show' => [['id'], ['_controller' => 'App\\Controller\\EvaluationController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evaluation']], [], [], []],
    'App\Controller\EvaluationController::create' => [[], ['_controller' => 'App\\Controller\\EvaluationController::create'], [], [['text', '/api/evaluation']], [], [], []],
    'App\Controller\EvaluationController::getEvaluationsByIdmodule' => [['idmodule'], ['_controller' => 'App\\Controller\\EvaluationController::getEvaluationsByIdmodule'], [], [['variable', '/', '[^/]++', 'idmodule', true], ['text', '/api/evaluation/idmodule']], [], [], []],
    'App\Controller\EvaluationController::getEvaluationByIdmoduleAndApprenant' => [['idmodule', 'apprenantId'], ['_controller' => 'App\\Controller\\EvaluationController::getEvaluationByIdmoduleAndApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'idmodule', true], ['text', '/api/evaluation/idmodule']], [], [], []],
    'App\Controller\EvaluationController::getEvaluationByQuizAndApprenant' => [['quizId', 'apprenantId'], ['_controller' => 'App\\Controller\\EvaluationController::getEvaluationByQuizAndApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'quizId', true], ['text', '/api/evaluation/quiz']], [], [], []],
    'App\Controller\EvaluationDetailController::create' => [[], ['_controller' => 'App\\Controller\\EvaluationDetailController::create'], [], [['text', '/api/evaluation-detail']], [], [], []],
    'App\Controller\EvaluationDetailController::getByQuizAndApprenant' => [['quizId', 'apprenantId'], ['_controller' => 'App\\Controller\\EvaluationDetailController::getByQuizAndApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'quizId', true], ['text', '/api/evaluation-detail/quiz']], [], [], []],
    'App\Controller\EvenementController::debug' => [[], ['_controller' => 'App\\Controller\\EvenementController::debug'], [], [['text', '/api/evenement/debug']], [], [], []],
    'App\Controller\EvenementController::list' => [[], ['_controller' => 'App\\Controller\\EvenementController::list'], [], [['text', '/api/evenement']], [], [], []],
    'App\Controller\EvenementController::show' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'App\Controller\EvenementController::create' => [[], ['_controller' => 'App\\Controller\\EvenementController::create'], [], [['text', '/api/evenement']], [], [], []],
    'App\Controller\EvenementController::update' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'App\Controller\EvenementController::delete' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'App\Controller\EvenementController::getByAdministrateur' => [['id'], ['_controller' => 'App\\Controller\\EvenementController::getByAdministrateur'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement/administrateur']], [], [], []],
    'App\Controller\EvenementController::getByDateRange' => [[], ['_controller' => 'App\\Controller\\EvenementController::getByDateRange'], [], [['text', '/api/evenement/date-range']], [], [], []],
    'App\Controller\EvenementController::getUpcoming' => [[], ['_controller' => 'App\\Controller\\EvenementController::getUpcoming'], [], [['text', '/api/evenement/upcoming']], [], [], []],
    'App\Controller\EvenementController::addAdministrateur' => [['id', 'adminId'], ['_controller' => 'App\\Controller\\EvenementController::addAdministrateur'], [], [['variable', '/', '[^/]++', 'adminId', true], ['text', '/administrateur'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'App\Controller\EvenementController::removeAdministrateur' => [['id', 'adminId'], ['_controller' => 'App\\Controller\\EvenementController::removeAdministrateur'], [], [['variable', '/', '[^/]++', 'adminId', true], ['text', '/administrateur'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/evenement']], [], [], []],
    'App\Controller\FileUploadController::uploadProfileImage' => [[], ['_controller' => 'App\\Controller\\FileUploadController::uploadProfileImage'], [], [['text', '/api/upload/profile-image']], [], [], []],
    'App\Controller\FixController::fix' => [[], ['_controller' => 'App\\Controller\\FixController::fix'], [], [['text', '/api/fix']], [], [], []],
    'App\Controller\FormateurController::getApprenants' => [[], ['_controller' => 'App\\Controller\\FormateurController::getApprenants'], [], [['text', '/api/formateur/apprenants']], [], [], []],
    'App\Controller\FormateurController::getApprenant' => [['id'], ['_controller' => 'App\\Controller\\FormateurController::getApprenant'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'App\Controller\FormateurController::getApprenantCours' => [['id'], ['_controller' => 'App\\Controller\\FormateurController::getApprenantCours'], [], [['text', '/cours'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'App\Controller\FormateurController::getCours' => [[], ['_controller' => 'App\\Controller\\FormateurController::getCours'], [], [['text', '/api/formateur/cours']], [], [], []],
    'App\Controller\FormateurController::getApprenantCoursByCategory' => [['id', 'category'], ['_controller' => 'App\\Controller\\FormateurController::getApprenantCoursByCategory'], [], [['variable', '/', '[^/]++', 'category', true], ['text', '/cours/category'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'App\Controller\FormateurController::getApprenantCoursAllCategories' => [['id'], ['_controller' => 'App\\Controller\\FormateurController::getApprenantCoursAllCategories'], [], [['text', '/cours/all-categories'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/formateur/apprenants']], [], [], []],
    'App\Controller\MessagerieController::getConversation' => [['formateurId', 'apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::getConversation'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'App\Controller\MessagerieController::getFormateurConversations' => [['formateurId'], ['_controller' => 'App\\Controller\\MessagerieController::getFormateurConversations'], [], [['text', '/conversations'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'App\Controller\MessagerieController::getApprenantConversations' => [['apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::getApprenantConversations'], [], [['text', '/conversations'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/messagerie/apprenant']], [], [], []],
    'App\Controller\MessagerieController::formateurEnvoyerMessage' => [['formateurId', 'apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::formateurEnvoyerMessage'], [], [['text', '/envoyer'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/apprenant'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'App\Controller\MessagerieController::apprenantEnvoyerMessage' => [['apprenantId', 'formateurId'], ['_controller' => 'App\\Controller\\MessagerieController::apprenantEnvoyerMessage'], [], [['text', '/envoyer'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/formateur'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/messagerie/apprenant']], [], [], []],
    'App\Controller\MessagerieController::marquerLu' => [['id'], ['_controller' => 'App\\Controller\\MessagerieController::marquerLu'], [], [['text', '/marquer-lu'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/messagerie']], [], [], []],
    'App\Controller\MessagerieController::getFormateursForApprenant' => [['apprenantId'], ['_controller' => 'App\\Controller\\MessagerieController::getFormateursForApprenant'], [], [['text', '/formateurs'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/messagerie/apprenant']], [], [], []],
    'App\Controller\MessagerieController::getApprenantsForFormateur' => [['formateurId'], ['_controller' => 'App\\Controller\\MessagerieController::getApprenantsForFormateur'], [], [['text', '/apprenants'], ['variable', '/', '[^/]++', 'formateurId', true], ['text', '/api/messagerie/formateur']], [], [], []],
    'App\Controller\NotificationController::getUserNotifications' => [[], ['_controller' => 'App\\Controller\\NotificationController::getUserNotifications'], [], [['text', '/api/notification']], [], [], []],
    'App\Controller\NotificationController::markAsRead' => [['id'], ['_controller' => 'App\\Controller\\NotificationController::markAsRead'], [], [['text', '/read'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/notification']], [], [], []],
    'App\Controller\NotificationController::markAllAsRead' => [[], ['_controller' => 'App\\Controller\\NotificationController::markAllAsRead'], [], [['text', '/api/notification/mark-all-read']], [], [], []],
    'App\Controller\NotificationController::deleteNotification' => [['id'], ['_controller' => 'App\\Controller\\NotificationController::deleteNotification'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/notification']], [], [], []],
    'App\Controller\ProgressionController::list' => [[], ['_controller' => 'App\\Controller\\ProgressionController::list'], [], [['text', '/api/progression']], [], [], []],
    'App\Controller\ProgressionController::show' => [['id'], ['_controller' => 'App\\Controller\\ProgressionController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/progression']], [], [], []],
    'App\Controller\ProgressionController::getProgressionByApprenant' => [['apprenantId'], ['_controller' => 'App\\Controller\\ProgressionController::getProgressionByApprenant'], [], [['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/progression/apprenant']], [], [], []],
    'App\Controller\ProgressionController::getProgressionByApprenantAndCours' => [['apprenantId', 'coursId'], ['_controller' => 'App\\Controller\\ProgressionController::getProgressionByApprenantAndCours'], [], [['variable', '/', '[^/]++', 'coursId', true], ['text', '/cours'], ['variable', '/', '[^/]++', 'apprenantId', true], ['text', '/api/progression/apprenant']], [], [], []],
    'App\Controller\QuizController::createBatch' => [[], ['_controller' => 'App\\Controller\\QuizController::createBatch'], [], [['text', '/api/quiz/batch']], [], [], []],
    'App\Controller\QuizController::list' => [[], ['_controller' => 'App\\Controller\\QuizController::list'], [], [['text', '/api/quiz']], [], [], []],
    'App\Controller\QuizController::show' => [['IDModule'], ['_controller' => 'App\\Controller\\QuizController::show'], [], [['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz']], [], [], []],
    'App\Controller\QuizController::update' => [['IDModule'], ['_controller' => 'App\\Controller\\QuizController::update'], [], [['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz']], [], [], []],
    'App\Controller\QuizController::delete' => [['id'], ['_controller' => 'App\\Controller\\QuizController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/quiz']], [], [], []],
    'App\Controller\QuizController::deleteByIdModule' => [['IDModule'], ['_controller' => 'App\\Controller\\QuizController::deleteByIdModule'], [], [['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/by-idmodule']], [], [], []],
    'App\Controller\QuizController::deleteCompetence' => [['IDModule', 'competenceId'], ['_controller' => 'App\\Controller\\QuizController::deleteCompetence'], [], [['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/competence']], [], [], []],
    'App\Controller\QuizController::deleteSousCompetence' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
    'App\Controller\QuizController::deleteAction' => [['IDModule', 'competenceId', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::deleteAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/action']], [], [], []],
    'App\Controller\QuizController::updateCompetence' => [['IDModule', 'competenceId'], ['_controller' => 'App\\Controller\\QuizController::updateCompetence'], [], [['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/competence']], [], [], []],
    'App\Controller\QuizController::updateSousCompetence' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
    'App\Controller\QuizController::updateSousCompetenceById' => [['IDModule', 'competenceId', 'id'], ['_controller' => 'App\\Controller\\QuizController::updateSousCompetenceById'], [], [['variable', '/', '[^/]++', 'id', true], ['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/sous-competence-by-id']], [], [], []],
    'App\Controller\QuizController::updateAction' => [['IDModule', 'competenceId', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::updateAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'competenceId', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/action']], [], [], []],
    'App\Controller\QuizController::createCompetence' => [[], ['_controller' => 'App\\Controller\\QuizController::createCompetence'], [], [['text', '/api/quiz/competence/create']], [], [], []],
    'App\Controller\QuizController::createSousCompetence' => [[], ['_controller' => 'App\\Controller\\SousCompetenceController::create'], [], [['text', '/api/sous-competence']], [], [], []],
    'App\Controller\QuizController::createAction' => [[], ['_controller' => 'App\\Controller\\QuizController::createAction'], [], [['text', '/api/quiz/action/create']], [], [], []],
    'App\Controller\QuizController::createQuizAction' => [[], ['_controller' => 'App\\Controller\\QuizController::createQuizAction'], [], [['text', '/api/quiz/quiz-action/create']], [], [], []],
    'App\Controller\QuizController::deleteQuizActionById' => [['id'], ['_controller' => 'App\\Controller\\QuizController::deleteQuizActionById'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/quiz/quiz-action-by-id']], [], [], []],
    'App\Controller\QuizController::deleteQuizAction' => [['IDModule', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::deleteQuizAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/quiz-action']], [], [], []],
    'App\Controller\QuizController::updateQuizActionById' => [['id'], ['_controller' => 'App\\Controller\\QuizController::updateQuizActionById'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/quiz/quiz-action-by-id']], [], [], []],
    'App\Controller\QuizController::updateQuizAction' => [['IDModule', 'actionNomFR', 'actionNomEN'], ['_controller' => 'App\\Controller\\QuizController::updateQuizAction'], [], [['variable', '/', '[^/]++', 'actionNomEN', true], ['variable', '/', '[^/]++', 'actionNomFR', true], ['variable', '/', '[^/]++', 'IDModule', true], ['text', '/api/quiz/quiz-action']], [], [], []],
    'App\Controller\ReclamationController::list' => [[], ['_controller' => 'App\\Controller\\ReclamationController::list'], [], [['text', '/api/reclamation']], [], [], []],
    'App\Controller\ReclamationController::getUserReclamations' => [[], ['_controller' => 'App\\Controller\\ReclamationController::getUserReclamations'], [], [['text', '/api/reclamation/user']], [], [], []],
    'App\Controller\ReclamationController::show' => [['id'], ['_controller' => 'App\\Controller\\ReclamationController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/reclamation']], [], [], []],
    'App\Controller\ReclamationController::create' => [[], ['_controller' => 'App\\Controller\\ReclamationController::create'], [], [['text', '/api/reclamation']], [], [], []],
    'App\Controller\ReclamationController::reply' => [['id'], ['_controller' => 'App\\Controller\\ReclamationController::reply'], [], [['text', '/reply'], ['variable', '/', '[^/]++', 'id', true], ['text', '/api/reclamation']], [], [], []],
    'App\Controller\SousCompetenceController::list' => [[], ['_controller' => 'App\\Controller\\SousCompetenceController::list'], [], [['text', '/api/sous-competence']], [], [], []],
    'App\Controller\SousCompetenceController::show' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
    'App\Controller\SousCompetenceController::create' => [[], ['_controller' => 'App\\Controller\\SousCompetenceController::create'], [], [['text', '/api/sous-competence']], [], [], []],
    'App\Controller\SousCompetenceController::update' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::update'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
    'App\Controller\SousCompetenceController::delete' => [['id'], ['_controller' => 'App\\Controller\\SousCompetenceController::delete'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/api/sous-competence']], [], [], []],
];
