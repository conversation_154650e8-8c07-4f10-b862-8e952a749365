@startuml Course Quiz Class Diagram

' Définition des classes principales
package "«View»" {
  class CoursView {
    -cours: Array
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleEdit(): void
    +handleAdd(): void
  }

  class QuizView {
    -quizzes: Array
    -selectedQuiz: Object
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleImportExcel(): void
    +handleAdd(): void
  }

  class CompetenceView {
    -competences: Array
    -selectedCompetence: Object
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleAdd(): void
  }

  class SousCompetenceView {
    -sousCompetences: Array
    -selectedSousCompetence: Object
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleAdd(): void
  }

  class ActionView {
    -actions: Array
    -selectedAction: Object
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleAdd(): void
  }
}

package "«Controller»" {
  class CourseController {
    -entityManager: EntityManagerInterface
    -coursRepository: CoursRepository
    -validator: ValidatorInterface
    +list(): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
  }

  class QuizController {
    -entityManager: EntityManagerInterface
    -coursRepository: CoursRepository
    -validator: ValidatorInterface
    +list(): JsonResponse
    +show(IDModule: string): JsonResponse
    +update(IDModule: string, request: Request): JsonResponse
    +delete(id: int): JsonResponse
    +deleteByIdModule(IDModule: string): JsonResponse
    +createBatch(request: Request): JsonResponse
    +createQuizAction(request: Request): JsonResponse
    +updateQuizActionById(id: int, request: Request): JsonResponse
    +deleteQuizActionById(id: int): JsonResponse
  }

  class CompetenceController {
    -entityManager: EntityManagerInterface
    -competenceRepository: CompetenceRepository
    -validator: ValidatorInterface
    +list(): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
  }

  class SousCompetenceController {
    -entityManager: EntityManagerInterface
    -sousCompetenceRepository: SousCompetenceRepository
    -validator: ValidatorInterface
    +list(): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
  }

  class ActionController {
    -entityManager: EntityManagerInterface
    -actionRepository: ActionRepository
    -validator: ValidatorInterface
    +list(): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
  }
}

package "«Model»" {
  class Cours {
    -id: int
    -titre: string
    -description: string
    -quizzes: Collection<Quiz>
    +getId(): int
    +getTitre(): string
    +setTitre(titre: string): self
    +getDescription(): string
    +setDescription(description: string): self
    +getQuizzes(): Collection<Quiz>
    +addQuiz(quiz: Quiz): self
    +removeQuiz(quiz: Quiz): self
  }

  class Quiz {
    -id: int
    -IDModule: string
    -type: string
    -category: string
    -mainSurface: boolean
    -main: int
    -surface: int
    -nomFR: string
    -nomEN: string
    -cours: Cours
    -competences: Collection<Competence>
    -actions: Collection<Action>
    +getId(): int
    +getIDModule(): string
    +setIDModule(IDModule: string): self
    +getType(): string
    +setType(type: string): self
    +getCategory(): string
    +setCategory(category: string): self
    +isMainSurface(): boolean
    +setMainSurface(mainSurface: boolean): self
    +getMain(): int
    +setMain(main: int): self
    +getSurface(): int
    +setSurface(surface: int): self
    +getNomFR(): string
    +setNomFR(nomFR: string): self
    +getNomEN(): string
    +setNomEN(nomEN: string): self
    +getCours(): Cours
    +setCours(cours: Cours): self
    +getCompetences(): Collection<Competence>
    +addCompetence(competence: Competence): self
    +removeCompetence(competence: Competence): self
    +getActions(): Collection<Action>
    +addAction(action: Action): self
    +removeAction(action: Action): self
  }

  class Competence {
    -id: int
    -idmodule: string
    -nom_fr: string
    -nom_en: string
    -categorie_fr: string
    -categorie_en: string
    -quiz: Quiz
    -sousCompetences: Collection<SousCompetence>
    +getId(): int
    +getIdmodule(): string
    +setIdmodule(idmodule: string): self
    +getNomFr(): string
    +setNomFr(nom_fr: string): self
    +getNomEn(): string
    +setNomEn(nom_en: string): self
    +getCategorieFr(): string
    +setCategorieFr(categorie_fr: string): self
    +getCategorieEn(): string
    +setCategorieEn(categorie_en: string): self
    +getQuiz(): Quiz
    +setQuiz(quiz: Quiz): self
    +getSousCompetences(): Collection<SousCompetence>
    +addSousCompetence(sousCompetence: SousCompetence): self
    +removeSousCompetence(sousCompetence: SousCompetence): self
    +synchronizeIdmodule(): void
  }

  class SousCompetence {
    -id: int
    -nom_fr: string
    -nom_en: string
    -competence: Competence
    +getId(): int
    +getNomFr(): string
    +setNomFr(nom_fr: string): self
    +getNomEn(): string
    +setNomEn(nom_en: string): self
    +getCompetence(): Competence
    +setCompetence(competence: Competence): self
  }

  class Action {
    -id: int
    -idmodule: string
    -nom_fr: string
    -nom_en: string
    -categorie_fr: string
    -categorie_en: string
    -quiz: Quiz
    +getId(): int
    +getIdmodule(): string
    +setIdmodule(idmodule: string): self
    +getNomFr(): string
    +setNomFr(nom_fr: string): self
    +getNomEn(): string
    +setNomEn(nom_en: string): self
    +getCategorieFr(): string
    +setCategorieFr(categorie_fr: string): self
    +getCategorieEn(): string
    +setCategorieEn(categorie_en: string): self
    +getQuiz(): Quiz
    +setQuiz(quiz: Quiz): self
    +synchronizeIdmodule(): void
  }
}

' Relations entre les classes
Cours "1" *-- "0..*" Quiz
Quiz "1" *-- "0..*" Competence
Quiz "1" *-- "0..*" Action
Competence "1" *-- "0..*" SousCompetence

' Relations entre les vues et les contrôleurs
CoursView ..> CourseController
QuizView ..> QuizController
CompetenceView ..> CompetenceController
SousCompetenceView ..> SousCompetenceController
ActionView ..> ActionController

' Relations entre les contrôleurs et les modèles
CourseController ..> Cours
QuizController ..> Quiz
QuizController ..> Action
CompetenceController ..> Competence
SousCompetenceController ..> SousCompetence
ActionController ..> Action

@enduml
