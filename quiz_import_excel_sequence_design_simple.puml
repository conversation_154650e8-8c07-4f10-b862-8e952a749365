@startuml Quiz Import Excel Sequence Design

title Diagramme de Séquence - Import d'un quiz via Excel

actor Administrateur
participant "«View»\nQuiz.jsx" as View
participant "«Library»\nXLSX" as XLSX
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model

Administrateur -> View : Sélectionner le fichier Excel
Administrateur -> View : Cliquer sur "Importer"
View -> View : handleExcelImport(file)

View -> XLSX : read(data)
XLSX --> View : workbook

View -> XLSX : utils.sheet_to_json(worksheet)
XLSX --> View : jsonData

View -> View : Extraire les données du quiz

View -> Controller : POST /api/quiz/batch
Controller -> Model : Enregistrer les données
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
