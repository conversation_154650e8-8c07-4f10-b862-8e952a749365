<?php

namespace App\Entity;

use App\Repository\CertificatRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CertificatRepository::class)]
class Certificat
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $dateObtention = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $contenu = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Apprenant $apprenant = null;

    #[ORM\OneToOne(inversedBy: 'certificat', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Progression $progression = null;

    /**
     * @var Collection<int, Notification>
     */
    #[ORM\OneToMany(targetEntity: Notification::class, mappedBy: 'certificat')]
    private Collection $notifications;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $isAutoGenerated = false;

    public function __construct()
    {
        $this->notifications = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDateObtention(): ?\DateTimeInterface
    {
        return $this->dateObtention;
    }

    public function setDateObtention(\DateTimeInterface $dateObtention): static
    {
        $this->dateObtention = $dateObtention;

        return $this;
    }

    public function getContenu(): ?string
    {
        return $this->contenu;
    }

    public function setContenu(?string $contenu): static
    {
        $this->contenu = $contenu;

        return $this;
    }

    public function getApprenant(): ?Apprenant
    {
        return $this->apprenant;
    }

    public function setApprenant(Apprenant $apprenant): static
    {
        $this->apprenant = $apprenant;

        return $this;
    }

    public function getProgression(): ?Progression
    {
        return $this->progression;
    }

    public function setProgression(?Progression $progression): static
    {
        // Si la progression est null, on ne fait rien
        if ($progression === null) {
            error_log('AVERTISSEMENT: Tentative d\'association d\'une progression null à un certificat');
            return $this;
        }

        // Vérifier que la progression a un ID, mais ne pas lancer d'exception
        if (!$progression->getId()) {
            error_log('AVERTISSEMENT: La progression n\'a pas d\'ID lors de l\'association à un certificat');
            // Au lieu de lancer une exception, on continue quand même
        }

        // Vérifier que la progression a bien un cours et un apprenant associés
        if (!$progression->getCours() || !$progression->getApprenant()) {
            error_log('AVERTISSEMENT: La progression n\'a pas de cours ou d\'apprenant associé');
            // On continue quand même
        }

        $this->progression = $progression;

        // Set the owning side of the relation if necessary
        // Mais éviter une boucle infinie
        try {
            // Vérifier si la progression a déjà un certificat
            $currentCertificat = $progression->getCertificat();

            // Si la progression a déjà un certificat différent de celui-ci
            if ($currentCertificat !== null && $currentCertificat !== $this) {
                error_log('AVERTISSEMENT: La progression est déjà associée à un autre certificat (ID: ' .
                    ($currentCertificat->getId() ?: 'non défini') . ')');

                // On continue quand même, mais on ne modifie pas la relation inverse
                return $this;
            }

            // Si la progression n'a pas de certificat ou a le même certificat
            if ($progression->getCertificat() !== $this) {
                // Désactiver temporairement la vérification bidirectionnelle
                try {
                    $progression->setCertificat($this);
                } catch (\Exception $e) {
                    error_log('Erreur ignorée lors de l\'établissement de la relation bidirectionnelle (setCertificat): ' . $e->getMessage());
                    // On continue quand même
                }
            }
        } catch (\Exception $e) {
            // Si une exception est levée, c'est probablement parce que
            // la progression n'est pas encore persistée. On ignore cette erreur
            // car la relation sera correctement établie après la persistance.
            error_log('Erreur ignorée lors de l\'établissement de la relation bidirectionnelle: ' . $e->getMessage());
        }

        return $this;
    }

    /**
     * @return Collection<int, Notification>
     */
    public function getNotifications(): Collection
    {
        return $this->notifications;
    }

    public function addNotification(Notification $notification): static
    {
        if (!$this->notifications->contains($notification)) {
            $this->notifications->add($notification);
            $notification->setCertificat($this);
        }

        return $this;
    }

    public function removeNotification(Notification $notification): static
    {
        if ($this->notifications->removeElement($notification)) {
            // set the owning side to null (unless already changed)
            if ($notification->getCertificat() === $this) {
                $notification->setCertificat(null);
            }
        }

        return $this;
    }

    public function isAutoGenerated(): bool
    {
        return $this->isAutoGenerated;
    }

    public function setIsAutoGenerated(bool $isAutoGenerated): static
    {
        $this->isAutoGenerated = $isAutoGenerated;

        return $this;
    }
}
