@startuml Quiz Import Excel Sequence Design

title Diagramme de Séquence - Import d'un quiz via Excel

actor Utilisateur
participant "«View»\nQuizImportPage.jsx" as View
participant "«Controller»\nQuizContext" as Context
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model
participant "«Service»\nExcelImportService" as Service

ref over Utilisateur, Service
  Importer un quiz via Excel
end ref

Utilisateur -> View : Cliquer sur "Importer un quiz via Excel"
View --> Utilisateur : Afficher le formulaire d'import Excel

Utilisateur -> View : Sélectionner le fichier Excel
Utilisateur -> View : Cliquer sur "Importer"
View -> Context : importQuizFromExcel(file, courseId)

Context -> Service : parseExcelFile(file)
Service --> Context : parsedData

Context -> Controller : POST /api/quiz/import
Controller -> Model : Valider les données
Model -> Model : beginTransaction()
Model -> Model : createQuiz(quizData)
Model -> Model : createCompetences(competencesData)
Model -> Model : createSousCompetences(sousCompetencesData)
Model -> Model : createActions(actionsData)
Model -> Model : commitTransaction()
Model --> Controller : response

alt [success: true]
    Controller --> Context : {success: true, quiz: data}
    Context --> View : Mettre à jour la liste des quiz
    View --> Utilisateur : Afficher "Quiz importé avec succès"
else [success: false]
    Controller --> Context : {success: false, error: messages}
    Context --> View : Transmettre les erreurs
    View --> Utilisateur : Afficher les erreurs
end

@enduml
