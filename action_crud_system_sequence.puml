@startuml Action CRUD System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Gestion des actions"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Sélectionner un cours
System --> Admin : Afficher les détails du cours

Admin -> System : Sélectionner un quiz
System --> Admin : Afficher les détails du quiz avec ses actions

== Ajout d'une action ==

Admin -> System : Cliquer sur "Ajouter une action"
System --> Admin : Afficher le formulaire d'ajout d'action

Admin -> System : Remplir le formulaire (nom_fr, nom_en, categorie_fr, categorie_en)
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Enregistrer l'action dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des actions avec la nouvelle action
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

== Modification d'une action ==

Admin -> System : Cliquer sur "Modifier" pour une action
System --> Admin : Afficher le formulaire de modification pré-rempli

Admin -> System : Modifier les champs de l'action
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Mettre à jour l'action dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des actions avec les modifications
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

== Suppression d'une action ==

Admin -> System : Cliquer sur "Supprimer" pour une action
System --> Admin : Afficher une confirmation de suppression

Admin -> System : Confirmer la suppression
System -> System : Supprimer l'action
System --> Admin : Afficher un message de succès
System --> Admin : Mettre à jour la liste des actions sans l'action supprimée

@enduml
