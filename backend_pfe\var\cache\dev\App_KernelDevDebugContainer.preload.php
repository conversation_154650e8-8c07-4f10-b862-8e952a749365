<?php

// This file has been auto-generated by the Symfony Dependency Injection Component
// You can reference it in the "opcache.preload" php.ini setting on PHP >= 7.4 when preloading is desired

use Symfony\Component\DependencyInjection\Dumper\Preloader;

if (in_array(PHP_SAPI, ['cli', 'phpdbg', 'embed'], true)) {
    return;
}

require dirname(__DIR__, 3).''.\DIRECTORY_SEPARATOR.'vendor/autoload.php';
(require __DIR__.'/App_KernelDevDebugContainer.php')->set(\ContainerJC7y5Zo\App_KernelDevDebugContainer::class, null);
require __DIR__.'/ContainerJC7y5Zo/EntityManagerGhostEbeb667.php';
require __DIR__.'/ContainerJC7y5Zo/RequestPayloadValueResolverGhost3590451.php';
require __DIR__.'/ContainerJC7y5Zo/ProfilerProxy0a5fddb.php';
require __DIR__.'/ContainerJC7y5Zo/getWebProfiler_Controller_RouterService.php';
require __DIR__.'/ContainerJC7y5Zo/getWebProfiler_Controller_ProfilerService.php';
require __DIR__.'/ContainerJC7y5Zo/getWebProfiler_Controller_ExceptionPanelService.php';
require __DIR__.'/ContainerJC7y5Zo/getValidator_WhenService.php';
require __DIR__.'/ContainerJC7y5Zo/getValidator_NotCompromisedPasswordService.php';
require __DIR__.'/ContainerJC7y5Zo/getValidator_NoSuspiciousCharactersService.php';
require __DIR__.'/ContainerJC7y5Zo/getValidator_ExpressionLanguageService.php';
require __DIR__.'/ContainerJC7y5Zo/getValidator_ExpressionService.php';
require __DIR__.'/ContainerJC7y5Zo/getValidator_EmailService.php';
require __DIR__.'/ContainerJC7y5Zo/getTwig_Runtime_SerializerService.php';
require __DIR__.'/ContainerJC7y5Zo/getTwig_Runtime_SecurityCsrfService.php';
require __DIR__.'/ContainerJC7y5Zo/getTwig_Runtime_ImportmapService.php';
require __DIR__.'/ContainerJC7y5Zo/getTwig_Runtime_HttpkernelService.php';
require __DIR__.'/ContainerJC7y5Zo/getTwig_Mailer_MessageListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getTwig_Form_RendererService.php';
require __DIR__.'/ContainerJC7y5Zo/getTwig_Form_EngineService.php';
require __DIR__.'/ContainerJC7y5Zo/getTurbo_Twig_RuntimeService.php';
require __DIR__.'/ContainerJC7y5Zo/getTurbo_Doctrine_EventListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_YmlService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_XliffService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_ResService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_QtService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_PoService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_PhpService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_MoService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_JsonService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_IniService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_DatService.php';
require __DIR__.'/ContainerJC7y5Zo/getTranslation_Loader_CsvService.php';
require __DIR__.'/ContainerJC7y5Zo/getTexter_TransportsService.php';
require __DIR__.'/ContainerJC7y5Zo/getStimulus_UxControllersTwigRuntimeService.php';
require __DIR__.'/ContainerJC7y5Zo/getStimulus_AssetMapper_LoaderJavascriptCompilerService.php';
require __DIR__.'/ContainerJC7y5Zo/getStimulus_AssetMapper_ControllersMapGeneratorService.php';
require __DIR__.'/ContainerJC7y5Zo/getSession_Handler_NativeService.php';
require __DIR__.'/ContainerJC7y5Zo/getSession_FactoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getServicesResetterService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Validator_UserPasswordService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_UserPasswordHasherService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_UserCheckerLocatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_User_Provider_Concrete_AppUserProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_RouteLoader_LogoutService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_PasswordHasherFactoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Logout_Listener_CsrfTokenClearingService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_UserProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_UserChecker_MainService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_UserChecker_LoginService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_UserChecker_ApiService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_Session_MainService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_PasswordMigratingService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_Main_UserProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_CsrfProtectionService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_CheckAuthenticatorCredentialsService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Listener_Api_UserProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_HttpUtilsService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_HelperService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Firewall_Map_Context_RegisterService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Firewall_Map_Context_PasswordResetService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Firewall_Map_Context_MainService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Firewall_Map_Context_LoginService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Firewall_Map_Context_DevService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Firewall_Map_Context_ApiService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Firewall_EventDispatcherLocatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Csrf_TokenStorageService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Csrf_TokenManagerService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_ChannelListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Authenticator_ManagersLocatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Authenticator_Manager_MainService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Authenticator_Manager_LoginService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Authenticator_Manager_ApiService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_Authenticator_JsonLogin_LoginService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_AccessMapService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecurity_AccessListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getSecrets_VaultService.php';
require __DIR__.'/ContainerJC7y5Zo/getRouting_LoaderService.php';
require __DIR__.'/ContainerJC7y5Zo/getPropertyInfo_SerializerExtractorService.php';
require __DIR__.'/ContainerJC7y5Zo/getNotifier_TransportFactory_NullService.php';
require __DIR__.'/ContainerJC7y5Zo/getMonolog_Logger_MessengerService.php';
require __DIR__.'/ContainerJC7y5Zo/getMonolog_Logger_MailerService.php';
require __DIR__.'/ContainerJC7y5Zo/getMonolog_Logger_DeprecationService.php';
require __DIR__.'/ContainerJC7y5Zo/getMonolog_Logger_ChatbotService.php';
require __DIR__.'/ContainerJC7y5Zo/getMonolog_Logger_AssetMapperService.php';
require __DIR__.'/ContainerJC7y5Zo/getMonolog_Handler_ChatbotService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_TransportFactoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Transport_Sync_FactoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Transport_FailedService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Transport_Doctrine_FactoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Transport_AsyncService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_RoutableMessageBusService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Retry_SendFailedMessageForRetryListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Retry_MultiplierRetryStrategy_FailedService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Retry_MultiplierRetryStrategy_AsyncService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Listener_StopWorkerOnRestartSignalListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Failure_SendFailedMessageToFailureTransportListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Bus_Default_Middleware_TraceableService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Bus_Default_Middleware_SendMessageService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessenger_Bus_Default_Middleware_HandleMessageService.php';
require __DIR__.'/ContainerJC7y5Zo/getMailer_TransportsService.php';
require __DIR__.'/ContainerJC7y5Zo/getMailer_TransportFactory_SmtpService.php';
require __DIR__.'/ContainerJC7y5Zo/getMailer_TransportFactory_SendmailService.php';
require __DIR__.'/ContainerJC7y5Zo/getMailer_TransportFactory_NullService.php';
require __DIR__.'/ContainerJC7y5Zo/getMailer_TransportFactory_NativeService.php';
require __DIR__.'/ContainerJC7y5Zo/getMailer_TransportFactory_GmailService.php';
require __DIR__.'/ContainerJC7y5Zo/getFragment_Renderer_InlineService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeGuesser_ValidatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeGuesser_DoctrineService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_Upload_ValidatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_Password_PasswordHasherService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_Form_ValidatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_Form_TransformationFailureHandlingService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_Form_PasswordHasherService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_Form_HttpFoundationService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_Form_DataCollectorService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_TypeExtension_CsrfService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_Type_FormService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_Type_FileService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_Type_EntityService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_Type_ColorService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_Type_ChoiceService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_ServerParamsService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_RegistryService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_Listener_PasswordHasherService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_FactoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getForm_ChoiceListFactory_CachedService.php';
require __DIR__.'/ContainerJC7y5Zo/getErrorHandler_ErrorRenderer_HtmlService.php';
require __DIR__.'/ContainerJC7y5Zo/getErrorControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_UuidGeneratorService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_UlidGeneratorService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_Validator_UniqueService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_Messenger_EventSubscriber_DoctrineClearEntityManagerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_Messenger_DoctrineSchemaListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_Listeners_PdoSessionHandlerSchemaListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_Listeners_LockStoreSchemaListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_Listeners_DoctrineTokenProviderSchemaListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_Listeners_DoctrineDbalCacheAdapterSchemaListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_DefaultListeners_AttachEntityListenersService.php';
require __DIR__.'/ContainerJC7y5Zo/getDoctrine_Orm_DefaultEntityManager_PropertyInfoExtractorService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_Security_Voter_VoteListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_Security_Firewall_Authenticator_MainService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_Security_Firewall_Authenticator_LoginService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_Security_Firewall_Authenticator_ApiService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_Security_EventDispatcher_LoginService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_Security_EventDispatcher_ApiService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_FileLinkFormatter_UrlFormatService.php';
require __DIR__.'/ContainerJC7y5Zo/getDebug_ErrorHandlerConfiguratorService.php';
require __DIR__.'/ContainerJC7y5Zo/getDataCollector_Request_SessionCollectorService.php';
require __DIR__.'/ContainerJC7y5Zo/getController_TemplateAttributeListenerService.php';
require __DIR__.'/ContainerJC7y5Zo/getContainer_GetenvService.php';
require __DIR__.'/ContainerJC7y5Zo/getContainer_GetRoutingConditionServiceService.php';
require __DIR__.'/ContainerJC7y5Zo/getContainer_EnvVarProcessorsLocatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getContainer_EnvVarProcessorService.php';
require __DIR__.'/ContainerJC7y5Zo/getCache_SystemClearerService.php';
require __DIR__.'/ContainerJC7y5Zo/getCache_GlobalClearerService.php';
require __DIR__.'/ContainerJC7y5Zo/getCache_AppClearerService.php';
require __DIR__.'/ContainerJC7y5Zo/getAssetMapper_Importmap_GeneratorService.php';
require __DIR__.'/ContainerJC7y5Zo/getAssetMapper_Importmap_ConfigReaderService.php';
require __DIR__.'/ContainerJC7y5Zo/getAssetMapper_Compiler_JavascriptImportPathCompilerService.php';
require __DIR__.'/ContainerJC7y5Zo/getAssetMapper_Compiler_CssAssetUrlCompilerService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Validator_State_ErrorProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_SwaggerUi_ProcessorService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_SwaggerUi_ContextService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_SwaggerUi_ActionService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_StateProvider_ObjectService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_StateProvider_Documentation_ContentNegotiationService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_StateProvider_CreateService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_StateProcessor_LocatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_StateProcessor_Documentation_WriteService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_State_ErrorProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Serializer_FilterParameterProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Openapi_ProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Metadata_PropertySchema_OneOfRestrictionService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Metadata_PropertySchema_CollectionRestrictionService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Listener_View_WriteService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Listener_View_ValidateService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Listener_View_SerializeService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Listener_View_RespondService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Listener_ExceptionService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Jsonld_Action_ContextService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_HttpCache_Processor_AddHeadersService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_State_RemoveProcessorService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_State_PersistProcessorService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_State_ItemProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_State_CollectionProviderService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_QueryExtension_PaginationService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_QueryExtension_FilterEagerLoadingService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_QueryExtension_FilterService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_QueryExtension_EagerLoadingService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_LinksHandlerService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Doctrine_Orm_Extension_ParameterExtensionService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Action_PlaceholderService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Action_NotFoundService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Action_NotExposedService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Action_ExceptionService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Action_ErrorPageService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Action_EntrypointService.php';
require __DIR__.'/ContainerJC7y5Zo/getApiPlatform_Action_DocumentationService.php';
require __DIR__.'/ContainerJC7y5Zo/getTemplateControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getRedirectControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getProfilerControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getEmailServiceService.php';
require __DIR__.'/ContainerJC7y5Zo/getJwtAuthenticatorService.php';
require __DIR__.'/ContainerJC7y5Zo/getUtilisateurRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getSousCompetenceRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getReclamationRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getQuizRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getProgressionRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getNotificationRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessagerieRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getFormateurRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getEvenementRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getEvaluationRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getEvaluationDetailRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getCoursRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getCompetenceRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getCertificatRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getApprenantRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getAdministrateurRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getActionRepositoryService.php';
require __DIR__.'/ContainerJC7y5Zo/getSousCompetenceControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getReclamationControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getQuizControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getProgressionControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getNotificationControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getMessagerieControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getFormateurControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getFixControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getFileUploadControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getEvenementControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getEvaluationDetailControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getEvaluationControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDiagnosticControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getDashboardControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getCourseControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getCompetenceControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getCertificatControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getAuthControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getApprenantControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getAdminControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getActionControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/getProcessorService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_ZcZ_KbZService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_Y4Zrx_Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_V5NSJjBService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_Qssr6JIService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_MOufrFGService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_J6IoHy0Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_F0CMdByService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_Cxvgam4Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_C7f47p7Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_PQOdg4mService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_OrpkBWhService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_O2p6Lk7Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_Mhqdd2rService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_MFVMisYService.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_DNIKdb2Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_ServiceLocator_Cj4aUBQService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Security_RequestMatcher_Vhy2oy3Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_Security_RequestMatcher_KLbKLHaService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Security_RequestMatcher_IRmxrxkService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Security_RequestMatcher_GOxUUePService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Security_RequestMatcher_0QxrXJtService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_VMw0m61Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_TGvt0LHService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_P4QvabmService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_KEzMhfsService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_XZowc_TService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_QXXNQ9dService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_Lml2ICsService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Messenger_HandlerDescriptor_6kVvRT_Service.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_Security_UserValueResolverService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_Security_SecurityTokenValueResolverService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_Doctrine_Orm_EntityValueResolverService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_VariadicService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_SessionService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_ServiceService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_RequestPayloadService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_RequestAttributeService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_RequestService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_QueryParameterValueResolverService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_NotTaggedControllerService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_DefaultService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_DatetimeService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ArgumentResolver_BackedEnumResolverService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_ValueResolver_ApiPlatform_ArgumentResolver_PayloadService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_Security_Voter_Security_Access_SimpleRoleVoterService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_Security_Voter_Security_Access_ExpressionVoterService.php';
require __DIR__.'/ContainerJC7y5Zo/get_Debug_Security_Voter_Security_Access_AuthenticatedVoterService.php';

$classes = [];
$classes[] = 'Symfony\Bundle\FrameworkBundle\FrameworkBundle';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\DoctrineBundle';
$classes[] = 'Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle';
$classes[] = 'Symfony\Bundle\DebugBundle\DebugBundle';
$classes[] = 'Symfony\Bundle\TwigBundle\TwigBundle';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\WebProfilerBundle';
$classes[] = 'Symfony\UX\StimulusBundle\StimulusBundle';
$classes[] = 'Symfony\UX\Turbo\TurboBundle';
$classes[] = 'Twig\Extra\TwigExtraBundle\TwigExtraBundle';
$classes[] = 'Symfony\Bundle\SecurityBundle\SecurityBundle';
$classes[] = 'Symfony\Bundle\MonologBundle\MonologBundle';
$classes[] = 'Symfony\Bundle\MakerBundle\MakerBundle';
$classes[] = 'Nelmio\CorsBundle\NelmioCorsBundle';
$classes[] = 'ApiPlatform\Symfony\Bundle\ApiPlatformBundle';
$classes[] = 'Symfony\Component\HttpKernel\Profiler\Profiler';
$classes[] = 'Symfony\Component\HttpKernel\Profiler\FileProfilerStorage';
$classes[] = 'Monolog\Logger';
$classes[] = 'Symfony\Component\Console\DataCollector\CommandDataCollector';
$classes[] = 'ApiPlatform\Symfony\Bundle\DataCollector\RequestDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\TimeDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\MemoryDataCollector';
$classes[] = 'Symfony\Component\Validator\DataCollector\ValidatorDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\AjaxDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\ExceptionDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\LoggerDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\EventDataCollector';
$classes[] = 'Symfony\Component\Translation\DataCollector\TranslationDataCollector';
$classes[] = 'Symfony\Bundle\SecurityBundle\DataCollector\SecurityDataCollector';
$classes[] = 'Symfony\Bridge\Twig\DataCollector\TwigDataCollector';
$classes[] = 'Symfony\Component\HttpClient\DataCollector\HttpClientDataCollector';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\DataCollector\DoctrineDataCollector';
$classes[] = 'Symfony\Component\Messenger\DataCollector\MessengerDataCollector';
$classes[] = 'Symfony\Component\Mailer\DataCollector\MessageDataCollector';
$classes[] = 'Symfony\Component\Notifier\DataCollector\NotificationDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\ConfigDataCollector';
$classes[] = 'Symfony\Component\HttpClient\TraceableHttpClient';
$classes[] = 'Symfony\Component\HttpClient\UriTemplateHttpClient';
$classes[] = 'Symfony\Contracts\HttpClient\HttpClientInterface';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\TraceableVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\ExpressionVoter';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Voter\RoleVoter';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\TraceableValueResolver';
$classes[] = 'ApiPlatform\Symfony\Bundle\ArgumentResolver\PayloadArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\BackedEnumValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DateTimeValueResolver';
$classes[] = 'Symfony\Component\Clock\Clock';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\NotTaggedControllerValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\QueryParameterValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\ServiceValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver';
$classes[] = 'Symfony\Bridge\Doctrine\ArgumentResolver\EntityValueResolver';
$classes[] = 'Symfony\Component\ExpressionLanguage\ExpressionLanguage';
$classes[] = 'Symfony\Component\Security\Http\Controller\SecurityTokenValueResolver';
$classes[] = 'Symfony\Component\Security\Http\Controller\UserValueResolver';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlerDescriptor';
$classes[] = 'Symfony\Component\HttpClient\Messenger\PingWebhookMessageHandler';
$classes[] = 'Symfony\Component\Notifier\Messenger\MessageHandler';
$classes[] = 'Symfony\Component\Process\Messenger\RunProcessMessageHandler';
$classes[] = 'Symfony\Component\Console\Messenger\RunCommandMessageHandler';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Console\Application';
$classes[] = 'Symfony\Component\Messenger\Handler\RedispatchMessageHandler';
$classes[] = 'Symfony\Component\Mailer\Messenger\MessageHandler';
$classes[] = 'Symfony\Component\Notifier\Transport\Transports';
$classes[] = 'Symfony\Component\Notifier\Transport';
$classes[] = 'Symfony\Component\HttpFoundation\ChainRequestMatcher';
$classes[] = 'Symfony\Component\HttpFoundation\RequestMatcher\PathRequestMatcher';
$classes[] = 'Symfony\Component\DependencyInjection\ServiceLocator';
$classes[] = 'Symfony\Component\HttpKernel\Debug\VirtualRequestStack';
$classes[] = 'ApiPlatform\Symfony\Messenger\Processor';
$classes[] = 'App\Controller\ActionController';
$classes[] = 'App\Controller\AdminController';
$classes[] = 'App\Controller\ApprenantController';
$classes[] = 'App\Controller\AuthController';
$classes[] = 'App\Controller\CertificatController';
$classes[] = 'App\Controller\CompetenceController';
$classes[] = 'App\Controller\CourseController';
$classes[] = 'App\Controller\DashboardController';
$classes[] = 'App\Controller\DiagnosticController';
$classes[] = 'App\Controller\EvaluationController';
$classes[] = 'App\Controller\EvaluationDetailController';
$classes[] = 'App\Controller\EvenementController';
$classes[] = 'App\Service\WebSocketNotificationService';
$classes[] = 'App\Controller\FileUploadController';
$classes[] = 'App\Controller\FixController';
$classes[] = 'App\Controller\FormateurController';
$classes[] = 'App\Controller\MessagerieController';
$classes[] = 'App\Controller\NotificationController';
$classes[] = 'App\Controller\ProgressionController';
$classes[] = 'App\Controller\QuizController';
$classes[] = 'App\Controller\ReclamationController';
$classes[] = 'App\Controller\SousCompetenceController';
$classes[] = 'App\Repository\ActionRepository';
$classes[] = 'App\Repository\AdministrateurRepository';
$classes[] = 'App\Repository\ApprenantRepository';
$classes[] = 'App\Repository\CertificatRepository';
$classes[] = 'App\Repository\CompetenceRepository';
$classes[] = 'App\Repository\CoursRepository';
$classes[] = 'App\Repository\EvaluationDetailRepository';
$classes[] = 'App\Repository\EvaluationRepository';
$classes[] = 'App\Repository\EvenementRepository';
$classes[] = 'App\Repository\FormateurRepository';
$classes[] = 'App\Repository\MessagerieRepository';
$classes[] = 'App\Repository\NotificationRepository';
$classes[] = 'App\Repository\ProgressionRepository';
$classes[] = 'App\Repository\QuizRepository';
$classes[] = 'App\Repository\ReclamationRepository';
$classes[] = 'App\Repository\SousCompetenceRepository';
$classes[] = 'App\Repository\UtilisateurRepository';
$classes[] = 'App\Security\JwtAuthenticator';
$classes[] = 'App\Service\EmailService';
$classes[] = 'Symfony\Component\Mailer\Mailer';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Controller\ProfilerController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\RedirectController';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\TemplateController';
$classes[] = 'ApiPlatform\Symfony\Action\DocumentationAction';
$classes[] = 'ApiPlatform\Symfony\Action\EntrypointAction';
$classes[] = 'ApiPlatform\Symfony\Action\ErrorPageAction';
$classes[] = 'ApiPlatform\Action\ExceptionAction';
$classes[] = 'ApiPlatform\Symfony\Action\NotExposedAction';
$classes[] = 'ApiPlatform\Symfony\Action\NotFoundAction';
$classes[] = 'ApiPlatform\Symfony\Action\PlaceholderAction';
$classes[] = 'Symfony\Component\Cache\Adapter\TraceableAdapter';
$classes[] = 'Symfony\Component\Cache\Adapter\AdapterInterface';
$classes[] = 'Symfony\Component\Cache\Adapter\AbstractAdapter';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\ParameterExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\State\LinksHandler';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\EagerLoadingExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\FilterExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\FilterEagerLoadingExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\OrderExtension';
$classes[] = 'ApiPlatform\Doctrine\Orm\Extension\PaginationExtension';
$classes[] = 'ApiPlatform\State\Pagination\Pagination';
$classes[] = 'ApiPlatform\Doctrine\Orm\State\CollectionProvider';
$classes[] = 'ApiPlatform\Doctrine\Orm\State\ItemProvider';
$classes[] = 'ApiPlatform\Doctrine\Common\State\PersistProcessor';
$classes[] = 'ApiPlatform\Doctrine\Common\State\RemoveProcessor';
$classes[] = 'ApiPlatform\HttpCache\State\AddHeadersProcessor';
$classes[] = 'ApiPlatform\State\Processor\AddLinkHeaderProcessor';
$classes[] = 'ApiPlatform\Hydra\State\HydraLinkProcessor';
$classes[] = 'ApiPlatform\State\Processor\RespondProcessor';
$classes[] = 'ApiPlatform\Hydra\Serializer\HydraPrefixNameConverter';
$classes[] = 'ApiPlatform\JsonSchema\BackwardCompatibleSchemaFactory';
$classes[] = 'ApiPlatform\Hydra\JsonSchema\SchemaFactory';
$classes[] = 'ApiPlatform\JsonSchema\SchemaFactory';
$classes[] = 'ApiPlatform\JsonSchema\TypeFactory';
$classes[] = 'ApiPlatform\JsonSchema\DefinitionNameFactory';
$classes[] = 'ApiPlatform\JsonLd\Action\ContextAction';
$classes[] = 'ApiPlatform\JsonLd\ContextBuilder';
$classes[] = 'ApiPlatform\Symfony\EventListener\ExceptionListener';
$classes[] = 'ApiPlatform\Symfony\EventListener\ErrorListener';
$classes[] = 'ApiPlatform\Symfony\EventListener\AddFormatListener';
$classes[] = 'ApiPlatform\State\Provider\ContentNegotiationProvider';
$classes[] = 'ApiPlatform\Symfony\EventListener\DeserializeListener';
$classes[] = 'ApiPlatform\Symfony\Security\State\AccessCheckerProvider';
$classes[] = 'ApiPlatform\State\Provider\DeserializeProvider';
$classes[] = 'ApiPlatform\Symfony\EventListener\ReadListener';
$classes[] = 'ApiPlatform\State\Provider\SecurityParameterProvider';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiProvider';
$classes[] = 'ApiPlatform\State\Provider\ParameterProvider';
$classes[] = 'ApiPlatform\Symfony\EventListener\RespondListener';
$classes[] = 'ApiPlatform\Symfony\EventListener\SerializeListener';
$classes[] = 'ApiPlatform\State\Processor\SerializeProcessor';
$classes[] = 'ApiPlatform\Symfony\EventListener\ValidateListener';
$classes[] = 'ApiPlatform\Symfony\Validator\State\ValidateProvider';
$classes[] = 'ApiPlatform\Symfony\Validator\Validator';
$classes[] = 'ApiPlatform\Symfony\EventListener\QueryParameterValidateListener';
$classes[] = 'ApiPlatform\Symfony\EventListener\WriteListener';
$classes[] = 'ApiPlatform\State\Processor\WriteProcessor';
$classes[] = 'ApiPlatform\Metadata\Operation\Factory\OperationMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\CachedPropertyMetadataFactory';
$classes[] = 'ApiPlatform\JsonSchema\Metadata\Property\Factory\SchemaPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\ValidatorPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\ExtractorPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\AttributePropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\IdentifierPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\DefaultPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\SerializerPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Doctrine\Orm\Metadata\Property\DoctrineOrmPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\PropertyInfoPropertyMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\CachedPropertyNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\ExtractorPropertyNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\ConcernsPropertyNameCollectionMetadataFactory';
$classes[] = 'ApiPlatform\Metadata\Property\Factory\PropertyInfoPropertyNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Extractor\XmlPropertyExtractor';
$classes[] = 'ApiPlatform\Metadata\Extractor\YamlPropertyExtractor';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaChoiceRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaCollectionRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaCountRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaFormat';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaGreaterThanOrEqualRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaGreaterThanRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaLengthRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaLessThanOrEqualRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaLessThanRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaOneOfRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaRangeRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaRegexRestriction';
$classes[] = 'ApiPlatform\Symfony\Validator\Metadata\Property\Restriction\PropertySchemaUniqueRestriction';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\CachedResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Doctrine\Orm\Metadata\Resource\DoctrineOrmResourceCollectionMetadataFactory';
$classes[] = 'ApiPlatform\Symfony\Messenger\Metadata\MessengerResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\PhpDocResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\AlternateUriResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\FiltersResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\FormatsResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\InputOutputResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\OperationNameResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\LinkResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Doctrine\Orm\Metadata\Resource\DoctrineOrmLinkFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\LinkFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\MainControllerResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\UriTemplateResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Operation\UnderscorePathSegmentNameGenerator';
$classes[] = 'ApiPlatform\Metadata\Util\Inflector';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\BackedEnumResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\NotExposedOperationResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ExtractorResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ConcernsResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ParameterResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\DeprecationResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\AttributesResourceMetadataCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\CachedResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ExtractorResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ClassNameResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\AttributesResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Resource\Factory\ConcernsResourceNameCollectionFactory';
$classes[] = 'ApiPlatform\Metadata\Extractor\XmlResourceExtractor';
$classes[] = 'ApiPlatform\Metadata\Extractor\YamlResourceExtractor';
$classes[] = 'Negotiation\Negotiator';
$classes[] = 'ApiPlatform\OpenApi\Factory\OpenApiFactory';
$classes[] = 'ApiPlatform\State\Pagination\PaginationOptions';
$classes[] = 'ApiPlatform\OpenApi\Options';
$classes[] = 'ApiPlatform\OpenApi\State\OpenApiProvider';
$classes[] = 'ApiPlatform\OpenApi\Serializer\SerializerContextBuilder';
$classes[] = 'ApiPlatform\Serializer\SerializerFilterContextBuilder';
$classes[] = 'ApiPlatform\Serializer\SerializerContextBuilder';
$classes[] = 'ApiPlatform\Metadata\ResourceClassResolver';
$classes[] = 'ApiPlatform\Symfony\Routing\Router';
$classes[] = 'ApiPlatform\Symfony\Security\ResourceAccessChecker';
$classes[] = 'ApiPlatform\Serializer\Parameter\SerializerFilterParameterProvider';
$classes[] = 'ApiPlatform\Serializer\Mapping\Factory\ClassMetadataFactory';
$classes[] = 'Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\LoaderChain';
$classes[] = 'Symfony\Component\Serializer\Mapping\Loader\AttributeLoader';
$classes[] = 'ApiPlatform\State\ErrorProvider';
$classes[] = 'ApiPlatform\State\CallableProcessor';
$classes[] = 'ApiPlatform\State\CreateProvider';
$classes[] = 'ApiPlatform\State\Provider\ReadProvider';
$classes[] = 'ApiPlatform\State\CallableProvider';
$classes[] = 'ApiPlatform\State\ObjectProvider';
$classes[] = 'ApiPlatform\Symfony\Validator\State\ParameterValidatorProvider';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiAction';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiContext';
$classes[] = 'ApiPlatform\Symfony\Bundle\SwaggerUi\SwaggerUiProcessor';
$classes[] = 'ApiPlatform\Symfony\Routing\IriConverter';
$classes[] = 'ApiPlatform\Metadata\IdentifiersExtractor';
$classes[] = 'ApiPlatform\Symfony\Routing\SkolemIriConverter';
$classes[] = 'ApiPlatform\Metadata\UriVariablesConverter';
$classes[] = 'ApiPlatform\Metadata\UriVariableTransformer\DateTimeUriVariableTransformer';
$classes[] = 'ApiPlatform\Metadata\UriVariableTransformer\IntegerUriVariableTransformer';
$classes[] = 'ApiPlatform\Symfony\Validator\State\ErrorProvider';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestPayloadValueResolver';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapper';
$classes[] = 'Symfony\Component\AssetMapper\Factory\CachedMappedAssetFactory';
$classes[] = 'Symfony\Component\AssetMapper\Factory\MappedAssetFactory';
$classes[] = 'Symfony\Component\AssetMapper\Path\PublicAssetsPathResolver';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperCompiler';
$classes[] = 'Symfony\Component\AssetMapper\CompiledAssetMapperConfigReader';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\CssAssetUrlCompiler';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\JavaScriptImportPathCompiler';
$classes[] = 'Symfony\Component\AssetMapper\Compiler\SourceMappingUrlsCompiler';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperDevServerSubscriber';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapConfigReader';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapGenerator';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\RemotePackageStorage';
$classes[] = 'Symfony\Component\AssetMapper\AssetMapperRepository';
$classes[] = 'Symfony\Component\Asset\Packages';
$classes[] = 'Symfony\Component\AssetMapper\MapperAwareAssetPackage';
$classes[] = 'Symfony\Component\Asset\PathPackage';
$classes[] = 'Symfony\Component\Asset\VersionStrategy\EmptyVersionStrategy';
$classes[] = 'Symfony\Component\Asset\Context\RequestStackContext';
$classes[] = 'Symfony\Component\Cache\Adapter\FilesystemAdapter';
$classes[] = 'Symfony\Component\HttpKernel\CacheClearer\Psr6CacheClearer';
$classes[] = 'Symfony\Component\Cache\Marshaller\DefaultMarshaller';
$classes[] = 'Symfony\Component\Cache\Adapter\ArrayAdapter';
$classes[] = 'Symfony\Component\Config\Resource\SelfCheckingResourceChecker';
$classes[] = 'Symfony\Component\Config\ResourceCheckerConfigCacheFactory';
$classes[] = 'Symfony\Component\DependencyInjection\EnvVarProcessor';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\CacheAttributeListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\IsGrantedAttributeListener';
$classes[] = 'Symfony\Bridge\Twig\EventListener\TemplateAttributeListener';
$classes[] = 'Symfony\Component\Cache\DataCollector\CacheDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\DumpDataCollector';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\FormDataCollector';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\FormDataExtractor';
$classes[] = 'Symfony\Component\HttpKernel\DataCollector\RequestDataCollector';
$classes[] = 'Symfony\Bundle\FrameworkBundle\DataCollector\RouterDataCollector';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DebugHandlersListener';
$classes[] = 'Symfony\Component\HttpKernel\Log\DebugLoggerConfigurator';
$classes[] = 'Symfony\Component\HttpKernel\Debug\ErrorHandlerConfigurator';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\FileLinkFormatter';
$classes[] = 'Symfony\Bridge\Monolog\Processor\DebugProcessor';
$classes[] = 'Symfony\Component\Security\Core\Authorization\TraceableAccessDecisionManager';
$classes[] = 'Symfony\Component\Security\Core\Authorization\AccessDecisionManager';
$classes[] = 'Symfony\Component\Security\Core\Authorization\Strategy\AffirmativeStrategy';
$classes[] = 'Symfony\Component\EventDispatcher\Debug\TraceableEventDispatcher';
$classes[] = 'Symfony\Component\EventDispatcher\EventDispatcher';
$classes[] = 'Symfony\Bundle\SecurityBundle\Debug\TraceableFirewallListener';
$classes[] = 'Symfony\Component\Security\Http\Authenticator\Debug\TraceableAuthenticatorManagerListener';
$classes[] = 'Symfony\Component\Security\Http\Firewall\AuthenticatorManagerListener';
$classes[] = 'Symfony\Bundle\SecurityBundle\EventListener\VoteListener';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableSerializer';
$classes[] = 'Symfony\Component\Serializer\Serializer';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UnwrappingDenormalizer';
$classes[] = 'ApiPlatform\OpenApi\Serializer\LegacyOpenApiNormalizer';
$classes[] = 'ApiPlatform\OpenApi\Serializer\ApiGatewayNormalizer';
$classes[] = 'ApiPlatform\OpenApi\Serializer\OpenApiNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ObjectNormalizer';
$classes[] = 'Symfony\Component\Serializer\Encoder\JsonEncoder';
$classes[] = 'ApiPlatform\Serializer\ConstraintViolationListNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\ConstraintViolationListNormalizer';
$classes[] = 'ApiPlatform\Problem\Serializer\ConstraintViolationListNormalizer';
$classes[] = 'ApiPlatform\Symfony\Validator\Serializer\ValidationExceptionNormalizer';
$classes[] = 'ApiPlatform\JsonLd\Serializer\ErrorNormalizer';
$classes[] = 'ApiPlatform\JsonLd\Serializer\ItemNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\DocumentationNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\EntrypointNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\ErrorNormalizer';
$classes[] = 'ApiPlatform\Serializer\ItemNormalizer';
$classes[] = 'ApiPlatform\Problem\Serializer\ErrorNormalizer';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\Normalizer\FlattenExceptionNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ProblemNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\UidNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ConstraintViolationListNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\MimeMessageNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\PropertyNormalizer';
$classes[] = 'Symfony\Component\Serializer\Mapping\ClassDiscriminatorFromClassMetadata';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateTimeZoneNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DateIntervalNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\FormErrorNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\BackedEnumNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\DataUriNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\TranslatableNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\JsonSerializableNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\CollectionFiltersNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\PartialCollectionViewNormalizer';
$classes[] = 'ApiPlatform\Hydra\Serializer\CollectionNormalizer';
$classes[] = 'Symfony\Component\Serializer\Normalizer\ArrayDenormalizer';
$classes[] = 'ApiPlatform\JsonLd\Serializer\ObjectNormalizer';
$classes[] = 'Symfony\Component\Serializer\Debug\TraceableEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\XmlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\YamlEncoder';
$classes[] = 'Symfony\Component\Serializer\Encoder\CsvEncoder';
$classes[] = 'ApiPlatform\Serializer\JsonEncoder';
$classes[] = 'ApiPlatform\Serializer\YamlEncoder';
$classes[] = 'Symfony\Component\Stopwatch\Stopwatch';
$classes[] = 'Symfony\Component\Validator\Validator\TraceableValidator';
$classes[] = 'Symfony\Component\Validator\Validator\ValidatorInterface';
$classes[] = 'Symfony\Component\DependencyInjection\Config\ContainerParametersResourceChecker';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\DisallowRobotsIndexingListener';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Registry';
$classes[] = 'Doctrine\DBAL\Connection';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\ConnectionFactory';
$classes[] = 'Doctrine\DBAL\Configuration';
$classes[] = 'Doctrine\DBAL\Schema\LegacySchemaManagerFactory';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager';
$classes[] = 'Doctrine\DBAL\Logging\Middleware';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Middleware\DebugMiddleware';
$classes[] = 'Doctrine\DBAL\Tools\DsnParser';
$classes[] = 'Symfony\Bridge\Doctrine\ContainerAwareEventManager';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Middleware\BacktraceDebugDataHolder';
$classes[] = 'Doctrine\ORM\Mapping\Driver\AttributeDriver';
$classes[] = 'Doctrine\ORM\Configuration';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Mapping\MappingDriver';
$classes[] = 'Doctrine\Persistence\Mapping\Driver\MappingDriverChain';
$classes[] = 'Doctrine\ORM\Mapping\UnderscoreNamingStrategy';
$classes[] = 'Doctrine\ORM\Mapping\DefaultQuoteStrategy';
$classes[] = 'Doctrine\ORM\Mapping\DefaultTypedFieldMapper';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Mapping\ContainerEntityListenerResolver';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Repository\ContainerRepositoryFactory';
$classes[] = 'Doctrine\ORM\Proxy\Autoloader';
$classes[] = 'Doctrine\ORM\EntityManager';
$classes[] = 'Symfony\Bridge\Doctrine\PropertyInfo\DoctrineExtractor';
$classes[] = 'Doctrine\ORM\Tools\AttachEntityListenersListener';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\ManagerConfigurator';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\DoctrineDbalCacheAdapterSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\RememberMeTokenProviderDoctrineSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\LockStoreSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\PdoSessionHandlerSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\SchemaListener\MessengerTransportDoctrineSchemaListener';
$classes[] = 'Symfony\Bridge\Doctrine\Messenger\DoctrineClearEntityManagerWorkerSubscriber';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntityValidator';
$classes[] = 'Symfony\Bridge\Doctrine\IdGenerator\UlidGenerator';
$classes[] = 'Symfony\Bridge\Doctrine\IdGenerator\UuidGenerator';
$classes[] = 'Doctrine\Bundle\MigrationsBundle\EventListener\SchemaFilterListener';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ErrorController';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\SerializerErrorRenderer';
$classes[] = 'Symfony\Bridge\Twig\ErrorRenderer\TwigErrorRenderer';
$classes[] = 'Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer';
$classes[] = 'Symfony\Component\HttpKernel\Debug\TraceableEventDispatcher';
$classes[] = 'Monolog\Handler\NullHandler';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ErrorListener';
$classes[] = 'Symfony\Component\Form\ChoiceList\Factory\CachingFactoryDecorator';
$classes[] = 'Symfony\Component\Form\ChoiceList\Factory\PropertyAccessDecorator';
$classes[] = 'Symfony\Component\Form\ChoiceList\Factory\DefaultChoiceListFactory';
$classes[] = 'Symfony\Component\Form\FormFactory';
$classes[] = 'Symfony\Component\Form\Extension\PasswordHasher\EventListener\PasswordHasherListener';
$classes[] = 'Symfony\Component\Form\FormRegistry';
$classes[] = 'Symfony\Component\Form\Extension\DependencyInjection\DependencyInjectionExtension';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\Proxy\ResolvedTypeFactoryDataCollectorProxy';
$classes[] = 'Symfony\Component\Form\ResolvedFormTypeFactory';
$classes[] = 'Symfony\Component\Form\Util\ServerParams';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\ChoiceType';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\ColorType';
$classes[] = 'Symfony\Bridge\Doctrine\Form\Type\EntityType';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\FileType';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\FormType';
$classes[] = 'Symfony\Component\Form\Extension\Csrf\Type\FormTypeCsrfExtension';
$classes[] = 'Symfony\Component\Form\Extension\DataCollector\Type\DataCollectorTypeExtension';
$classes[] = 'Symfony\Component\Form\Extension\HttpFoundation\Type\FormTypeHttpFoundationExtension';
$classes[] = 'Symfony\Component\Form\Extension\HttpFoundation\HttpFoundationRequestHandler';
$classes[] = 'Symfony\Component\Form\Extension\PasswordHasher\Type\FormTypePasswordHasherExtension';
$classes[] = 'Symfony\Component\Form\Extension\Core\Type\TransformationFailureExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\FormTypeValidatorExtension';
$classes[] = 'Symfony\Component\Form\Extension\PasswordHasher\Type\PasswordTypePasswordHasherExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\RepeatedTypeValidatorExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\SubmitTypeValidatorExtension';
$classes[] = 'Symfony\Component\Form\Extension\Validator\Type\UploadValidatorExtension';
$classes[] = 'Symfony\Bridge\Doctrine\Form\DoctrineOrmTypeGuesser';
$classes[] = 'Symfony\Component\Form\Extension\Validator\ValidatorTypeGuesser';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\InlineFragmentRenderer';
$classes[] = 'Symfony\Component\HttpClient\HttpClient';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\HttpKernelRunner';
$classes[] = 'Symfony\Component\Runtime\Runner\Symfony\ResponseRunner';
$classes[] = 'Symfony\Component\Runtime\SymfonyRuntime';
$classes[] = 'Symfony\Component\HttpKernel\HttpKernel';
$classes[] = 'Symfony\Component\HttpKernel\Controller\TraceableControllerResolver';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Controller\ControllerResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\TraceableArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\Controller\ArgumentResolver';
$classes[] = 'Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadataFactory';
$classes[] = 'App\Kernel';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleAwareListener';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\LocaleListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\EnvelopeListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageLoggerListener';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessengerTransportListener';
$classes[] = 'Symfony\Component\Mailer\Bridge\Google\Transport\GmailTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\NativeTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\NullTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\SendmailTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Smtp\EsmtpTransportFactory';
$classes[] = 'Symfony\Component\Mailer\Transport\Transports';
$classes[] = 'Symfony\Component\Mailer\Transport';
$classes[] = 'Symfony\Component\Messenger\Middleware\AddBusNameStampMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\HandleMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Handler\HandlersLocator';
$classes[] = 'Symfony\Component\Messenger\Middleware\SendMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Transport\Sender\SendersLocator';
$classes[] = 'Symfony\Component\Messenger\Middleware\TraceableMiddleware';
$classes[] = 'Symfony\Component\Messenger\TraceableMessageBus';
$classes[] = 'Symfony\Component\Messenger\MessageBus';
$classes[] = 'Symfony\Component\Messenger\EventListener\AddErrorDetailsStampListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\SendFailedMessageToFailureTransportListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnRestartSignalListener';
$classes[] = 'Symfony\Component\Messenger\EventListener\StopWorkerOnCustomStopExceptionListener';
$classes[] = 'Symfony\Component\Messenger\Middleware\DispatchAfterCurrentBusMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\FailedMessageProcessingMiddleware';
$classes[] = 'Symfony\Component\Messenger\Middleware\RejectRedeliveredMessageMiddleware';
$classes[] = 'Symfony\Component\Messenger\Retry\MultiplierRetryStrategy';
$classes[] = 'Symfony\Component\Messenger\EventListener\SendFailedMessageForRetryListener';
$classes[] = 'Symfony\Component\Messenger\RoutableMessageBus';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportInterface';
$classes[] = 'Symfony\Component\Messenger\Bridge\Doctrine\Transport\DoctrineTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\InMemory\InMemoryTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\Serialization\PhpSerializer';
$classes[] = 'Symfony\Component\Messenger\Transport\Sync\SyncTransportFactory';
$classes[] = 'Symfony\Component\Messenger\Transport\TransportFactory';
$classes[] = 'Symfony\Component\Mime\MimeTypes';
$classes[] = 'Monolog\Handler\RotatingFileHandler';
$classes[] = 'Symfony\Bridge\Monolog\Handler\ConsoleHandler';
$classes[] = 'Monolog\Handler\StreamHandler';
$classes[] = 'Monolog\Processor\PsrLogMessageProcessor';
$classes[] = 'Nelmio\CorsBundle\EventListener\CacheableResponseVaryListener';
$classes[] = 'Nelmio\CorsBundle\EventListener\CorsListener';
$classes[] = 'Nelmio\CorsBundle\Options\Resolver';
$classes[] = 'Nelmio\CorsBundle\Options\ConfigProvider';
$classes[] = 'Symfony\Component\Notifier\EventListener\NotificationLoggerListener';
$classes[] = 'Symfony\Component\Notifier\Transport\NullTransportFactory';
$classes[] = 'Symfony\Component\DependencyInjection\ParameterBag\ContainerBag';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ProfilerListener';
$classes[] = 'Symfony\Component\PropertyAccess\PropertyAccessor';
$classes[] = 'Symfony\Component\PropertyInfo\PropertyInfoExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\PhpStanExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor';
$classes[] = 'Symfony\Component\PropertyInfo\Extractor\SerializerExtractor';
$classes[] = 'Symfony\Component\HttpFoundation\RequestStack';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ResponseListener';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\Router';
$classes[] = 'Symfony\Component\Routing\Matcher\ExpressionLanguageProvider';
$classes[] = 'Symfony\Component\Routing\RequestContext';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\RouterListener';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\DelegatingLoader';
$classes[] = 'Symfony\Component\Config\Loader\LoaderResolver';
$classes[] = 'Symfony\Component\Routing\Loader\XmlFileLoader';
$classes[] = 'Symfony\Component\HttpKernel\Config\FileLocator';
$classes[] = 'Symfony\Component\Routing\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\GlobFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\DirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\ContainerLoader';
$classes[] = 'ApiPlatform\Symfony\Routing\ApiLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Routing\AttributeRouteControllerLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeDirectoryLoader';
$classes[] = 'Symfony\Component\Routing\Loader\AttributeFileLoader';
$classes[] = 'Symfony\Component\Routing\Loader\Psr4DirectoryLoader';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Secrets\SodiumVault';
$classes[] = 'Symfony\Component\String\LazyString';
$classes[] = 'Symfony\Component\Security\Http\Firewall\AccessListener';
$classes[] = 'Symfony\Component\Security\Http\AccessMap';
$classes[] = 'Symfony\Component\Security\Core\Authentication\AuthenticationTrustResolver';
$classes[] = 'Symfony\Component\Security\Http\Authenticator\JsonLoginAuthenticator';
$classes[] = 'Symfony\Component\Security\Http\Authentication\AuthenticatorManager';
$classes[] = 'Symfony\Component\Security\Core\Authorization\AuthorizationChecker';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ChannelListener';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ContextListener';
$classes[] = 'Symfony\Component\Security\Csrf\CsrfTokenManager';
$classes[] = 'Symfony\Component\Security\Csrf\TokenGenerator\UriSafeTokenGenerator';
$classes[] = 'Symfony\Component\Security\Csrf\TokenStorage\SessionTokenStorage';
$classes[] = 'Symfony\Component\Security\Core\Authorization\ExpressionLanguage';
$classes[] = 'ApiPlatform\Symfony\Security\Core\Authorization\ExpressionLanguageProvider';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallMap';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallContext';
$classes[] = 'Symfony\Component\Security\Http\Firewall\ExceptionListener';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\FirewallConfig';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security\LazyFirewallContext';
$classes[] = 'Symfony\Bundle\SecurityBundle\Security';
$classes[] = 'Symfony\Component\Security\Http\HttpUtils';
$classes[] = 'Symfony\Component\Security\Http\EventListener\UserProviderListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CheckCredentialsListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CsrfProtectionListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\PasswordMigratingListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\SessionStrategyListener';
$classes[] = 'Symfony\Component\Security\Http\Session\SessionAuthenticationStrategy';
$classes[] = 'Symfony\Component\Security\Http\EventListener\UserCheckerListener';
$classes[] = 'Symfony\Component\Security\Http\EventListener\CsrfTokenClearingLogoutListener';
$classes[] = 'Symfony\Component\Security\Http\Logout\LogoutUrlGenerator';
$classes[] = 'Symfony\Component\PasswordHasher\Hasher\PasswordHasherFactory';
$classes[] = 'Symfony\Component\Security\Core\Role\RoleHierarchy';
$classes[] = 'Symfony\Bundle\SecurityBundle\Routing\LogoutRouteLoader';
$classes[] = 'Symfony\Component\Security\Core\Authentication\Token\Storage\UsageTrackingTokenStorage';
$classes[] = 'Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage';
$classes[] = 'Symfony\Bridge\Doctrine\Security\User\EntityUserProvider';
$classes[] = 'Symfony\Component\Security\Core\User\InMemoryUserChecker';
$classes[] = 'Symfony\Component\PasswordHasher\Hasher\UserPasswordHasher';
$classes[] = 'Symfony\Component\Security\Core\Validator\Constraints\UserPasswordValidator';
$classes[] = 'Symfony\Component\Serializer\DataCollector\SerializerDataCollector';
$classes[] = 'Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter';
$classes[] = 'Symfony\Component\DependencyInjection\ContainerInterface';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\ServicesResetter';
$classes[] = 'Symfony\Component\HttpFoundation\Session\SessionFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\NativeSessionStorageFactory';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\MetadataBag';
$classes[] = 'Symfony\Component\HttpFoundation\Session\Storage\Handler\StrictSessionHandler';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\SessionListener';
$classes[] = 'Symfony\Component\String\Slugger\AsciiSlugger';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\ControllersMapGenerator';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\AutoImportLocator';
$classes[] = 'Symfony\UX\StimulusBundle\AssetMapper\StimulusLoaderJavaScriptCompiler';
$classes[] = 'Symfony\UX\StimulusBundle\Ux\UxPackageReader';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\UxControllersTwigRuntime';
$classes[] = 'Symfony\Component\Translation\Loader\CsvFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuDatFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IniFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\JsonFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\MoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PhpFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\PoFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\QtFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\IcuResFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\XliffFileLoader';
$classes[] = 'Symfony\Component\Translation\Loader\YamlFileLoader';
$classes[] = 'Symfony\Component\Translation\LocaleSwitcher';
$classes[] = 'Symfony\Component\Translation\DataCollectorTranslator';
$classes[] = 'Symfony\Bundle\FrameworkBundle\Translation\Translator';
$classes[] = 'Symfony\Component\Translation\Formatter\MessageFormatter';
$classes[] = 'Symfony\Component\Translation\IdentityTranslator';
$classes[] = 'Symfony\UX\Turbo\Doctrine\BroadcastListener';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\TwigBroadcaster';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\ImuxBroadcaster';
$classes[] = 'Symfony\UX\Turbo\Broadcaster\IdAccessor';
$classes[] = 'Symfony\UX\Turbo\Request\RequestListener';
$classes[] = 'Symfony\UX\Turbo\Twig\TurboRuntime';
$classes[] = 'Twig\Cache\FilesystemCache';
$classes[] = 'Twig\Extension\CoreExtension';
$classes[] = 'Twig\Extension\EscaperExtension';
$classes[] = 'Twig\Extension\OptimizerExtension';
$classes[] = 'Twig\Extension\StagingExtension';
$classes[] = 'Twig\ExtensionSet';
$classes[] = 'Twig\Template';
$classes[] = 'Twig\TemplateWrapper';
$classes[] = 'Twig\Environment';
$classes[] = 'Twig\Loader\FilesystemLoader';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\DumpExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ProfilerExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\TranslationExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\AssetExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\CodeExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\RoutingExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\YamlExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\StopwatchExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ExpressionExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpFoundationExtension';
$classes[] = 'Symfony\Component\HttpFoundation\UrlHelper';
$classes[] = 'Symfony\Bridge\Twig\Extension\WebLinkExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\FormExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\ImportMapExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\LogoutUrlExtension';
$classes[] = 'Symfony\Bridge\Twig\Extension\SecurityExtension';
$classes[] = 'Symfony\Component\Security\Http\Impersonate\ImpersonateUrlGenerator';
$classes[] = 'Doctrine\Bundle\DoctrineBundle\Twig\DoctrineExtension';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Twig\WebProfilerExtension';
$classes[] = 'Symfony\Component\VarDumper\Dumper\HtmlDumper';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\UxControllersTwigExtension';
$classes[] = 'Symfony\UX\Turbo\Twig\TwigExtension';
$classes[] = 'Symfony\UX\StimulusBundle\Twig\StimulusTwigExtension';
$classes[] = 'Symfony\UX\StimulusBundle\Helper\StimulusHelper';
$classes[] = 'Symfony\Bridge\Twig\AppVariable';
$classes[] = 'Twig\RuntimeLoader\ContainerRuntimeLoader';
$classes[] = 'Twig\Extra\TwigExtraBundle\MissingExtensionSuggestor';
$classes[] = 'Symfony\Bundle\TwigBundle\DependencyInjection\Configurator\EnvironmentConfigurator';
$classes[] = 'Symfony\Bridge\Twig\Form\TwigRendererEngine';
$classes[] = 'Symfony\Component\Form\FormRenderer';
$classes[] = 'Symfony\Component\Mailer\EventListener\MessageListener';
$classes[] = 'Symfony\Bridge\Twig\Mime\BodyRenderer';
$classes[] = 'Twig\Profiler\Profile';
$classes[] = 'Symfony\Bridge\Twig\Extension\HttpKernelRuntime';
$classes[] = 'Symfony\Component\HttpKernel\DependencyInjection\LazyLoadingFragmentHandler';
$classes[] = 'Symfony\Component\HttpKernel\Fragment\FragmentUriGenerator';
$classes[] = 'Symfony\Component\HttpFoundation\UriSigner';
$classes[] = 'Symfony\Bridge\Twig\Extension\ImportMapRuntime';
$classes[] = 'Symfony\Component\AssetMapper\ImportMap\ImportMapRenderer';
$classes[] = 'Symfony\Bridge\Twig\Extension\CsrfRuntime';
$classes[] = 'Symfony\Bridge\Twig\Extension\SerializerRuntime';
$classes[] = 'Symfony\Component\HttpKernel\EventListener\ValidateRequestListener';
$classes[] = 'Symfony\Component\Validator\ValidatorBuilder';
$classes[] = 'Symfony\Component\Validator\Validation';
$classes[] = 'Symfony\Component\Validator\ContainerConstraintValidatorFactory';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\DoctrineInitializer';
$classes[] = 'Symfony\Component\Validator\Mapping\Loader\PropertyInfoLoader';
$classes[] = 'Symfony\Bridge\Doctrine\Validator\DoctrineLoader';
$classes[] = 'Symfony\Component\Validator\Constraints\EmailValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\ExpressionValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\ExpressionLanguageProvider';
$classes[] = 'Symfony\Component\Validator\Constraints\NoSuspiciousCharactersValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\NotCompromisedPasswordValidator';
$classes[] = 'Symfony\Component\Validator\Constraints\WhenValidator';
$classes[] = 'Symfony\Component\VarDumper\Cloner\VarCloner';
$classes[] = 'Symfony\Component\VarDumper\Server\Connection';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\SourceContextProvider';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\RequestContextProvider';
$classes[] = 'Symfony\Component\VarDumper\Dumper\ContextProvider\CliContextProvider';
$classes[] = 'Symfony\Component\WebLink\EventListener\AddLinkHeaderListener';
$classes[] = 'Symfony\Component\WebLink\HttpHeaderSerializer';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\ExceptionPanelController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\ProfilerController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Controller\RouterController';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Csp\ContentSecurityPolicyHandler';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\Csp\NonceGenerator';
$classes[] = 'Symfony\Bundle\WebProfilerBundle\EventListener\WebDebugToolbarListener';

$preloaded = Preloader::preload($classes);
