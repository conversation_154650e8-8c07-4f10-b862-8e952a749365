@startuml Sprint 3 - Diagramme 2A : Visualisation de Progression par l'Apprenant - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Apprenant" as A

participant "«View»\nDashboardView" as DV

participant "«Controller»\nProgressionController" as PC

participant "«Model»\nApprenant" as AM
participant "«Model»\nProgression" as PM
participant "«Model»\nCours" as CoM
participant "«Model»\nEvaluation" as EM

== Visualisation de la progression par l'apprenant ==

A -> DV : Accéder au tableau de bord
DV -> PC : getProgressionByApprenant(apprenantId)
PC -> AM : find(apprenantId)
AM --> PC : apprenant
PC -> PM : findBy(['apprenant' => apprenant])
PM --> PC : progressions[]
PC -> CoM : findAll()
CoM --> PC : cours[]
PC -> EM : findBy(['apprenant' => apprenant])
EM --> PC : evaluations[]
PC -> PC : calculateOverallProgress(progressions, evaluations)
PC -> PC : checkCertificateAvailability(progressions)
PC --> DV : JsonResponse avec progressionData et certificats
DV --> A : afficher tableau de bord avec progression

note right of DV : Affichage des cours avec :\n- Pourcentages de progression\n- is_completed = true si 100%\n- Certificats disponibles

A -> DV : Consulter détails d'un cours
DV -> PC : getProgressionByApprenantAndCours(apprenantId, coursId)
PC -> AM : find(apprenantId)
AM --> PC : apprenant
PC -> CoM : find(coursId)
CoM --> PC : cours
PC -> PM : findOneBy(['apprenant' => apprenant, 'cours' => cours])
PM --> PC : progression
PC --> DV : JsonResponse avec détails progression
DV --> A : afficher progression détaillée

note right of DV : Détails de progression\navec quiz complétés\net certificats disponibles

@enduml
