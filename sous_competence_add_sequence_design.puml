@startuml Sous-Competence Add Sequence Design

title Diagramme de Séquence - Ajout d'une sous-compétence

actor Utilisateur
participant "«View»\nSousCompetenceForm.jsx" as View
participant "«Controller»\nSousCompetenceContext" as Context
participant "«Controller»\nSousCompetenceController" as Controller
participant "«Model»\nSousCompetence" as Model

ref over Utilisateur, Model
  Ajouter une sous-compétence
end ref

Utilisateur -> View : Cliquer sur "Ajouter une sous-compétence"
View --> Utilisateur : Afficher le formulaire d'ajout de sous-compétence

Utilisateur -> View : Remplir le formulaire (nom_fr, nom_en)
Utilisateur -> View : Cliquer sur "Enregistrer"
View -> View : Valider les données du formulaire
View -> Context : addSousCompetence(sousCompetenceData)

Context -> Controller : POST /api/sous_competence
Controller -> Model : Valider les données
Model -> Model : create(sousCompetenceData)
Model -> Model : persist(sousCompetence)
Model --> Controller : response

alt [success: true]
    Controller --> Context : {success: true, sousCompetence: data}
    Context --> View : Mettre à jour la liste des sous-compétences
    View --> Utilisateur : Afficher "Sous-compétence ajoutée avec succès"
else [success: false]
    Controller --> Context : {success: false, error: messages}
    Context --> View : Transmettre les erreurs
    View --> Utilisateur : Afficher les erreurs
end

@enduml
