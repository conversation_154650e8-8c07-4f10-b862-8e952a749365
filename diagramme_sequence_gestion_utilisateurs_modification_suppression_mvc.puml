@startuml Diagramme de Séquence - Gestion des Utilisateurs (Modification et Suppression)

actor Administrateur
participant "<<View>>\nUsersManagementPage" as View
participant "<<Controller>>\nAdminController" as Controller
participant "<<Model>>\nUtilisateur" as Model

title Diagramme de Séquence - Gestion des Utilisateurs (Modification et Suppression) - Architecture MVC

ref over Administrateur, Model : Gérer les utilisateurs - Modification et Suppression

== Modifier un utilisateur ==

Administrateur -> View : Cliquer sur l'icône "Modifier" d'un utilisateur
activate View

View -> Controller : getUserById(userId)
activate Controller

Controller -> Model : find(userId)
activate Model
Model --> Controller : user
deactivate Model

Controller --> View : user
deactivate Controller

View --> Administrateur : afficher formulaire de modification avec données pré-remplies

Administrateur -> View : Modifier les informations
Administrateur -> View : Cliquer sur "Enregistrer"
activate View

View -> Controller : updateUser(userId, userData)
activate Controller

Controller -> Controller : validate(userData)

alt données invalides
    Controller --> View : errors
    View --> Administrateur : afficher les erreurs
else données valides
    Controller -> Model : find(userId)
    activate Model
    Model --> Controller : user
    deactivate Model
    
    Controller -> Model : update(user, userData)
    activate Model
    Model --> Controller : updatedUser
    deactivate Model
    
    Controller -> Model : save(updatedUser)
    activate Model
    Model --> Controller : savedUser
    deactivate Model
    
    Controller --> View : success
    View --> Administrateur : afficher "Utilisateur modifié avec succès"
    View --> Administrateur : mettre à jour la liste des utilisateurs
end

deactivate Controller
deactivate View

== Supprimer un utilisateur ==

Administrateur -> View : Cliquer sur l'icône "Supprimer" d'un utilisateur
activate View

View --> Administrateur : afficher boîte de dialogue de confirmation

Administrateur -> View : Confirmer la suppression
View -> Controller : deleteUser(userId)
activate Controller

Controller -> Model : find(userId)
activate Model
Model --> Controller : user
deactivate Model

Controller -> Model : delete(user)
activate Model
Model --> Controller : deleted
deactivate Model

Controller --> View : success
deactivate Controller

View --> Administrateur : afficher "Utilisateur supprimé avec succès"
View --> Administrateur : mettre à jour la liste des utilisateurs

deactivate View

@enduml
