@startuml Quiz Add System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Ajout d'un quiz"

Admin -> System : Accéder à la page du formulaire de quiz
System --> Admin : Afficher le formulaire du quiz (avec sections optionnelles)

Admin -> System : Remplir les informations obligatoires du quiz

opt [Ajouter des compétences]
    Admin -> System : Remplir les données des compétences
    
    opt [Ajouter des sous-compétences]
        Admin -> System : Remplir les données des sous-compétences
    end
end

opt [Ajouter des actions]
    Admin -> System : Remplir les données des actions
end

Admin -> System : Cliquer sur "Enregistrer"

System -> System : Valider les données du quiz
System -> System : Valider les compétences (si présentes)
System -> System : Valider les sous-compétences (si présentes)
System -> System : Valider les actions (si présentes)

alt [Toutes les données valides]
    System -> System : Démarrer transaction base de données
    System -> System : Enregistrer le quiz
    
    opt [Compétences présentes]
        System -> System : Créer les compétences associées
        System -> System : Synchroniser IDModule pour compétences
        
        opt [Sous-compétences présentes]
            System -> System : Créer les sous-compétences associées
        end
    end
    
    opt [Actions présentes]
        System -> System : Créer les actions associées
        System -> System : Synchroniser IDModule pour actions
    end
    
    System -> System : Valider la transaction
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des quiz du cours
else [Données invalides]
    System -> System : Annuler la transaction
    System --> Admin : Afficher les erreurs de validation spécifiques
end

@enduml
