@startuml Sprint Use Case Diagram - Gestion des Cours et Quiz

!theme plain
skinparam backgroundColor #FAFAFA
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1976D2
    FontSize 12
    FontStyle bold
}

skinparam usecase {
    BackgroundColor #FFFFFF
    BorderColor #424242
    FontColor #212121
    FontSize 11
    ArrowColor #424242
}

skinparam package {
    BackgroundColor #F5F5F5
    BorderColor #9E9E9E
    FontColor #424242
    FontSize 12
    FontStyle bold
}

' Définition des acteurs
actor "Administrateur" as Admin #LightBlue
actor "Apprenant" as Apprenant #LightGreen
actor "Formateur" as Formateur #LightYellow

' Package principal pour la gestion des cours
package "Gestion des Cours et Quiz" {

    ' Cas d'utilisation principaux pour les cours
    usecase "Ajouter un cours" as UC1
    usecase "Modifier un cours" as UC2
    usecase "Supprimer un cours" as UC3
    usecase "Consulter la liste des cours" as UC4

    ' Cas d'utilisation principaux pour les quiz
    usecase "Ajouter un quiz" as UC5
    usecase "Importer un quiz via Excel" as UC6
    usecase "Modifier un quiz" as UC7
    usecase "Supprimer un quiz" as UC8

    ' Cas d'utilisation pour les compétences
    usecase "Ajouter une compétence" as UC9
    usecase "Modifier une compétence" as UC10
    usecase "Supprimer une compétence" as UC11

    ' Cas d'utilisation pour les sous-compétences
    usecase "Ajouter une sous-compétence" as UC12
    usecase "Modifier une sous-compétence" as UC13
    usecase "Supprimer une sous-compétence" as UC14

    ' Cas d'utilisation pour les actions
    usecase "Ajouter une action" as UC15
    usecase "Modifier une action" as UC16
    usecase "Supprimer une action" as UC17

    ' Cas d'utilisation de support/validation
    usecase "Valider les données du cours" as UC_ValidateCourse
    usecase "Valider les données du quiz" as UC_ValidateQuiz
    usecase "Associer quiz au cours" as UC_AssociateQuiz
    usecase "Valider format Excel" as UC_ValidateExcel
    usecase "Parser fichier Excel" as UC_ParseExcel
    usecase "Associer compétence au quiz" as UC_AssociateCompetence
    usecase "Associer sous-compétence à compétence" as UC_AssociateSousCompetence
    usecase "Associer action au quiz" as UC_AssociateAction
    usecase "Synchroniser IDModule" as UC_SyncIDModule
}

' Relations des acteurs avec les cas d'utilisation

' Administrateur - Gestion complète
Admin --> UC1
Admin --> UC2
Admin --> UC3
Admin --> UC4
Admin --> UC5
Admin --> UC6
Admin --> UC7
Admin --> UC8
Admin --> UC9
Admin --> UC10
Admin --> UC11
Admin --> UC12
Admin --> UC13
Admin --> UC14
Admin --> UC15
Admin --> UC16
Admin --> UC17

' Apprenant - Consultation uniquement
Apprenant --> UC4

' Formateur - Consultation uniquement
Formateur --> UC4

' Relations include (dépendances obligatoires)

' Pour la gestion des cours
UC1 ..> UC_ValidateCourse : <<include>>
UC2 ..> UC_ValidateCourse : <<include>>

' Pour la gestion des quiz
UC5 ..> UC_ValidateQuiz : <<include>>
UC5 ..> UC_AssociateQuiz : <<include>>
UC7 ..> UC_ValidateQuiz : <<include>>

' Pour l'import Excel
UC6 ..> UC_ValidateExcel : <<include>>
UC6 ..> UC_ParseExcel : <<include>>
UC6 ..> UC_ValidateQuiz : <<include>>
UC6 ..> UC_AssociateQuiz : <<include>>

' Pour la gestion des compétences
UC9 ..> UC_AssociateCompetence : <<include>>
UC9 ..> UC_SyncIDModule : <<include>>
UC10 ..> UC_SyncIDModule : <<include>>

' Pour la gestion des sous-compétences
UC12 ..> UC_AssociateSousCompetence : <<include>>
UC13 ..> UC_AssociateSousCompetence : <<include>>

' Pour la gestion des actions
UC15 ..> UC_AssociateAction : <<include>>
UC15 ..> UC_SyncIDModule : <<include>>
UC16 ..> UC_SyncIDModule : <<include>>

' Relations extend (fonctionnalités optionnelles)

' Extension pour la création de quiz avec compétences et actions (OPTIONNELLES)
UC5 <.. UC9 : <<extend>>
UC5 <.. UC15 : <<extend>>

' Extension pour l'import Excel avec création automatique de compétences et actions (OPTIONNELLES)
UC6 <.. UC9 : <<extend>>
UC6 <.. UC12 : <<extend>>
UC6 <.. UC15 : <<extend>>

' Extension pour la gestion des sous-compétences lors de la création de compétences (OPTIONNELLES)
UC9 <.. UC12 : <<extend>>

' Notes explicatives
note right of UC6
  L'import Excel peut créer automatiquement
  le quiz, ses compétences, sous-compétences
  et actions en une seule opération
end note

note right of UC_SyncIDModule
  Synchronise automatiquement l'IDModule
  entre Quiz, Compétences et Actions
  pour maintenir la cohérence des données
end note

note bottom of UC4
  Accessible par tous les rôles :
  - Administrateur (gestion complète)
  - Apprenant (consultation)
  - Formateur (consultation)
end note

note top of UC5
  Un quiz peut être créé SANS compétences
  ni actions. Ces éléments sont optionnels
  et peuvent être ajoutés ultérieurement.
end note

@enduml
