@startuml Sprint4_Classes_Participantes

skinparam backgroundColor #FAFAFA
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1976D2
    FontSize 12
    FontStyle bold
}

skinparam circle {
    BackgroundColor #FFFFFF
    BorderColor #424242
    FontColor #212121
    FontSize 11
    BorderThickness 2
}

skinparam arrow {
    Color #424242
    FontSize 10
}

title Sprint 4 - Messagerie et Notifications\nDiagramme de Classes Participantes

' Acteur principal
actor "Utilisate<PERSON>" as User

' Couche View (Frontend React)
circle "Messagerie" as Messagerie
circle "FormateurMessagerie" as FormateurMessagerie
circle "NotificationCenter" as NotificationCenter
circle "ApprenantReclamation" as ApprenantReclamation
circle "AdminReclamation" as AdminReclamation

' Couche Controller (Backend Symfony)
circle "MessagerieController" as MessagerieController
circle "NotificationController" as NotificationController
circle "ReclamationController" as ReclamationController

' Couche Entity (Backend Doctrine)
circle "MessagerieEntity" as MessagerieEntity
circle "NotificationEntity" as NotificationEntity
circle "ReclamationEntity" as ReclamationEntity
circle "FormateurEntity" as FormateurEntity
circle "ApprenantEntity" as ApprenantEntity
circle "AdministrateurEntity" as AdministrateurEntity

' Relations Utilisateur vers Views
User --> Messagerie
User --> FormateurMessagerie
User --> NotificationCenter
User --> ApprenantReclamation
User --> AdminReclamation

' Relations Views vers Controllers
Messagerie --> MessagerieController
FormateurMessagerie --> MessagerieController
NotificationCenter --> NotificationController
ApprenantReclamation --> ReclamationController
AdminReclamation --> ReclamationController

' Relations Controllers vers Entities
MessagerieController --> MessagerieEntity
MessagerieController --> FormateurEntity
MessagerieController --> ApprenantEntity
MessagerieController --> NotificationEntity

NotificationController --> NotificationEntity

ReclamationController --> ReclamationEntity
ReclamationController --> AdministrateurEntity
ReclamationController --> NotificationEntity

' Relations entre Entities
MessagerieEntity --> FormateurEntity
MessagerieEntity --> ApprenantEntity
MessagerieEntity --> NotificationEntity

ReclamationEntity --> NotificationEntity

NotificationEntity --> MessagerieEntity
NotificationEntity --> ReclamationEntity

@enduml
