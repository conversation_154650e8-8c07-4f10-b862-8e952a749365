@startuml Course Modify System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Modification d'un cours"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Cliquer sur "Modifier" pour un cours
System --> Admin : Afficher le formulaire de modification pré-rempli

Admin -> System : Modifier les champs (titre, description)
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Mettre à jour le cours dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des cours avec les modifications
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

@enduml
