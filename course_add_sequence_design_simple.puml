@startuml Course Add Sequence Design

title Diagramme de Séquence - Ajout d'un cours

actor Administrateur
participant "«View»\nCours.jsx" as View
participant "«Controller»\nCourseController" as Controller
participant "«Model»\nCours" as Model

Administrateur -> View : Remplir le formulaire (titre, description)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : POST /api/cours
Controller -> Model : Enregistrer le cours
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Affiche<PERSON> le résultat

@enduml
