<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Cours extends \App\Entity\Cours implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'administrateurs' => [parent::class, 'administrateurs', null, 16],
        "\0".parent::class."\0".'apprenants' => [parent::class, 'apprenants', null, 16],
        "\0".parent::class."\0".'description' => [parent::class, 'description', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'progressions' => [parent::class, 'progressions', null, 16],
        "\0".parent::class."\0".'quizzes' => [parent::class, 'quizzes', null, 16],
        "\0".parent::class."\0".'titre' => [parent::class, 'titre', null, 16],
        'administrateurs' => [parent::class, 'administrateurs', null, 16],
        'apprenants' => [parent::class, 'apprenants', null, 16],
        'description' => [parent::class, 'description', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'progressions' => [parent::class, 'progressions', null, 16],
        'quizzes' => [parent::class, 'quizzes', null, 16],
        'titre' => [parent::class, 'titre', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}
