@startuml Diagramme de Classes Participantes - Sprint 3 (É<PERSON>uation et Suivi)

skinparam classAttributeIconSize 0
skinparam classFontStyle bold
skinparam classFontSize 14
skinparam classBackgroundColor white
skinparam classBorderColor black
skinparam classBorderThickness 2
skinparam classStereotypeFontSize 12
skinparam circledCharacterFontSize 12
skinparam circledCharacterRadius 12
skinparam circledCharacterFontName Arial
skinparam defaultFontName Arial
skinparam arrowThickness 2
skinparam arrowColor black

' Acteur
actor Utilisateur

' Vues Frontend
circle "ApprenantCours" as ApprenantCoursView
circle "QuizDetails" as QuizDetailsView
circle "Dashboard" as DashboardView
circle "FormateurDashboardPage" as FormateurDashboardView
circle "CertificateDisplay" as CertificateDisplayView
circle "CourseManagementPage" as CourseManagementView
circle "CalendarPage" as CalendarPageView

' Contrôleurs Backend
circle "EvaluationController" as EvaluationController
circle "ProgressionController" as ProgressionController
circle "CertificatController" as CertificatController
circle "DashboardController" as DashboardController
circle "EvenementController" as EvenementController

' Entités
circle "Evaluation" as EvaluationEntity
circle "Progression" as ProgressionEntity
circle "Certificat" as CertificatEntity
circle "Apprenant" as ApprenantEntity
circle "Formateur" as FormateurEntity
circle "Cours" as CoursEntity
circle "Evenement" as EvenementEntity
circle "Notification" as NotificationEntity

' Relations avec l'utilisateur
Utilisateur -- ApprenantCoursView
Utilisateur -- QuizDetailsView
Utilisateur -- DashboardView
Utilisateur -- FormateurDashboardView
Utilisateur -- CertificateDisplayView
Utilisateur -- CourseManagementView
Utilisateur -- CalendarPageView

' Relations Frontend vers Backend
ApprenantCoursView -- ProgressionController
QuizDetailsView -- EvaluationController
DashboardView -- ProgressionController
FormateurDashboardView -- DashboardController
CertificateDisplayView -- CertificatController
CourseManagementView -- CertificatController
CalendarPageView -- EvenementController

' Relations Contrôleurs vers Entités
EvaluationController -- EvaluationEntity
EvaluationController -- ApprenantEntity
EvaluationController -- FormateurEntity

ProgressionController -- ProgressionEntity
ProgressionController -- ApprenantEntity
ProgressionController -- CoursEntity

CertificatController -- CertificatEntity
CertificatController -- ApprenantEntity
CertificatController -- ProgressionEntity

DashboardController -- EvaluationEntity
DashboardController -- CertificatEntity

EvenementController -- EvenementEntity
EvenementController -- NotificationEntity
EvenementController -- ApprenantEntity
EvenementController -- FormateurEntity

@enduml
