@startuml Quiz Add Sequence Design

title Diagramme de Séquence - Ajout d'un quiz

actor Utilisateur
participant "«View»\nQuizManagementPage.jsx" as View
participant "«Controller»\nQuizContext" as Context
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model

ref over <PERSON>tilisa<PERSON><PERSON>, Model
  Ajouter un quiz à un cours
end ref

Utilisateur -> View : Cliquer sur "Ajouter un quiz"
View --> Utilisateur : Afficher le formulaire d'ajout de quiz

Utilisateur -> View : Remplir le formulaire (IDModule, Category, Type, MainSurface)
Utilisateur -> View : Cliquer sur "Enregistrer"
View -> View : Valider les données du formulaire
View -> Context : addQuiz(quizData)

Context -> Controller : POST /api/quiz
Controller -> Model : Valider les données
Model -> Model : create(quizData)
Model -> Model : persist(quiz)
Model --> Controller : response

alt [success: true]
    Controller --> Context : {success: true, quiz: data}
    Context --> View : Mettre à jour la liste des quiz
    View --> Utilisateur : Afficher "Quiz ajouté avec succès"
else [success: false]
    Controller --> Context : {success: false, error: messages}
    Context --> View : Transmettre les erreurs
    View --> Utilisateur : Afficher les erreurs
end

@enduml
