@startuml Sprint 3 - Diagramme 3A : Consultation des Statistiques - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Administrateur" as Admin
actor "Formateur" as F

participant "«View»\nFormateurDashboardView" as FDV

participant "«Controller»\nDashboardController" as DC

participant "«Model»\nUtilisateur" as UM
participant "«Model»\nCours" as CoM
participant "«Model»\nEvaluation" as EM
participant "«Model»\nCertificat" as CM
participant "«Model»\nFormateur" as FM

== Consultation des statistiques par administrateur ==

Admin -> FDV : Accéder au dashboard administrateur
FDV -> DC : getStats()
DC -> UM : count(['isApproved' => true])
UM --> DC : $totalUsers
DC -> UM : count(['role' => 'apprenant', 'isApproved' => true])
UM --> DC : $totalApprenants
DC -> CoM : count([])
CoM --> DC : $totalCourses
DC -> EM : count([])
EM --> DC : $totalEvaluations
DC -> CM : count([])
CM --> DC : $totalCertificats
DC -> DC : calculateGrowthRate($lastMonth, $current)
DC --> FDV : JsonResponse avec statistiques complètes
FDV --> Admin : afficher dashboard avec stats

note right of FDV : Dashboard avec métriques :\n- Nombre d'apprenants\n- Cours actifs\n- Évaluations réalisées\n- Certificats délivrés

== Consultation des statistiques par formateur ==

F -> FDV : Accéder au dashboard formateur
FDV -> DC : getFormateurStats()
DC -> FM : findOneBy(['utilisateur' => user])
FM --> DC : formateur
DC -> EM : findBy(['formateur' => formateur])
EM --> DC : evaluations[]
DC -> DC : calculateFormateurStats(evaluations)
DC --> FDV : JsonResponse avec statistiques formateur
FDV --> F : afficher dashboard formateur

note right of FDV : Statistiques spécifiques :\n- Évaluations effectuées\n- Taux de réussite\n- Apprenants suivis

@enduml
