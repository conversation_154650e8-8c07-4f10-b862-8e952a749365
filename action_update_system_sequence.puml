@startuml Action Update System Sequence

actor "Administrateur" as Admin
participant "Syst<PERSON>" as System

title "Diagramme de séquence système : Modification d'une action"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Sélectionner un cours
System --> Admin : Afficher les détails du cours

Admin -> System : Sélectionner un quiz
System --> Admin : Afficher les détails du quiz avec ses actions

Admin -> System : Cliquer sur "Modifier" pour une action
System --> Admin : Afficher le formulaire de modification pré-rempli

Admin -> System : Modifier les champs de l'action
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Mettre à jour l'action dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des actions avec les modifications
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

@enduml
