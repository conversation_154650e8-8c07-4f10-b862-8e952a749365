@startuml Diagramme de Séquence - Sélection et Évaluation des Compétences

participant "Formateur" as F
participant "«View»\nQuizDetails.jsx" as V
participant "«Controller»\nAdminController" as AC

participant "«Controller»\nQuizController" as QC
participant "«Controller»\nCompetenceController" as CC
participant "«Model»\nApprenant" as MA
participant "«Model»\nCours" as MC
participant "«Model»\nQuiz" as MQ
participant "«Model»\nCompetence" as MCO

== Sélection d'un cours d'apprenant ==

note over F, V : Le formateur navigue via :\nApprenantsList.jsx → ApprenantCours.jsx → CourseQuizzes.jsx → QuizDetails.jsx

F -> V : Naviguer vers QuizDetails depuis CourseQuizzes
activate V

note over V : QuizDetails.jsx charge automatiquement :\n- Quiz data via QuizService.getQuizByIdModule()\n- Compétences via QuizService.getCompetencesByQuiz()\n- Actions via QuizService.getActions()

V -> QC : getQuizByIdModule(IDModule)
activate QC
QC -> MQ : findBy(['IDModule' => IDModule])
activate MQ
MQ --> QC : quiz
deactivate MQ
QC --> V : JsonResponse(quiz)
deactivate QC

V -> CC : getCompetencesByQuiz(idmodule)
activate CC
CC -> MCO : findByIdModule(idmodule)
activate MCO
MCO --> CC : competences
deactivate MCO
CC --> V : JsonResponse(competences)
deactivate CC

V --> F : afficher interface d'évaluation complète
deactivate V

== Évaluation des compétences ==

note over F, MCO : Les compétences sont évaluées via EvaluationDetailController\nqui gère les détails d'évaluation pour chaque compétence

loop Pour chaque compétence
    F -> V : Attribuer un statut à la compétence
    activate V

    alt Compétence acquise
        V -> CC : update(Request, competenceId)
        activate CC
        CC -> MCO : setStatut("Acquise")
        activate MCO
        MCO -> MCO : entityManager->flush()
        MCO --> CC : competence
        deactivate MCO
        CC --> V : JsonResponse(['message' => 'success'])
        deactivate CC
        V --> F : mettre à jour le statut en vert

    else Compétence à améliorer
        V -> CC : update(Request, competenceId)
        activate CC
        CC -> MCO : setStatut("À améliorer")
        activate MCO
        MCO -> MCO : entityManager->flush()
        MCO --> CC : competence
        deactivate MCO
        CC --> V : JsonResponse(['message' => 'success'])
        deactivate CC
        V --> F : mettre à jour le statut en orange

        note over F, V : Gestion des sous-compétences via SousCompetenceController

    else Compétence non acquise
        V -> CC : update(Request, competenceId)
        activate CC
        CC -> MCO : setStatut("Non acquise")
        activate MCO
        MCO -> MCO : entityManager->flush()
        MCO --> CC : competence
        deactivate MCO
        CC --> V : JsonResponse(['message' => 'success'])
        deactivate CC
        V --> F : mettre à jour le statut en rouge
    end
    deactivate V
end

@enduml
