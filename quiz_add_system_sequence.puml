@startuml Quiz Add System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Ajout d'un quiz"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Sélectionner un cours
System --> Admin : Afficher les détails du cours et ses quiz

Admin -> System : Cliquer sur "Ajouter un quiz"
System --> Admin : Afficher le formulaire d'ajout de quiz

Admin -> System : Remplir le formulaire du quiz (IDModule, Category, Type, MainSurface)

opt [Gestion des compétences]
    Admin -> System : Ajouter un bloc de compétence
    Admin -> System : Supprimer un bloc de compétence
end

opt [Gestion des sous-compétences]
    Admin -> System : Ajouter un bloc de sous-compétence
    Admin -> System : Supprimer un bloc de sous-compétence
end

opt [Gestion des actions]
    Admin -> System : Ajouter un bloc d'action
    Admin -> System : Supprimer un bloc d'action
end

Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Enregistrer le quiz complet dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des quiz du cours avec le nouveau quiz
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

@enduml
