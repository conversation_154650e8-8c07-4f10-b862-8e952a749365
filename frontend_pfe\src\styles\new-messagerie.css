/* Nouveau design pour la messagerie */

/* Variables de couleurs */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #f3f4f6;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --dark-bg: #1e293b;
  --dark-secondary: #334155;
  --dark-border: #475569;
  --dark-text: #f8fafc;
  --dark-text-secondary: #94a3b8;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Conteneur principal */
.new-messagerie {
  display: flex;
  height: calc(100vh - 120px);
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow);
  position: relative;
}

.dark .new-messagerie {
  background-color: var(--dark-bg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* En-tête principal */
.new-messagerie-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: white;
}

.dark .new-messagerie-header {
  background-color: var(--dark-bg);
  border-color: var(--dark-border);
}

.new-messagerie-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dark .new-messagerie-title {
  color: var(--dark-text);
}

.new-messagerie-title-icon {
  color: var(--primary-color);
}

.new-messagerie-actions {
  display: flex;
  gap: 0.5rem;
}

.new-messagerie-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.new-messagerie-action-btn:hover {
  background-color: var(--secondary-color);
  color: var(--text-primary);
}

.dark .new-messagerie-action-btn {
  color: var(--dark-text-secondary);
}

.dark .new-messagerie-action-btn:hover {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
}

/* Conteneur de la liste des conversations */
.new-messagerie-conversations {
  width: 320px;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  background-color: white;
}

.dark .new-messagerie-conversations {
  background-color: var(--dark-bg);
  border-color: var(--dark-border);
}

/* Barre de recherche */
.new-messagerie-search {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.dark .new-messagerie-search {
  border-color: var(--dark-border);
}

.new-messagerie-search-input {
  position: relative;
  width: 100%;
}

.new-messagerie-search-input input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 9999px;
  border: 1px solid var(--border-color);
  background-color: var(--secondary-color);
  font-size: 0.875rem;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.new-messagerie-search-input input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.dark .new-messagerie-search-input input {
  background-color: var(--dark-secondary);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

.dark .new-messagerie-search-input input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.new-messagerie-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

.dark .new-messagerie-search-icon {
  color: var(--dark-text-secondary);
}

/* Liste des conversations */
.new-messagerie-conversation-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.new-messagerie-conversation-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-left: 3px solid transparent;
}

.new-messagerie-conversation-item:hover {
  background-color: var(--secondary-color);
}

.new-messagerie-conversation-item.active {
  background-color: rgba(59, 130, 246, 0.1);
  border-left-color: var(--primary-color);
}

.dark .new-messagerie-conversation-item:hover {
  background-color: var(--dark-secondary);
}

.dark .new-messagerie-conversation-item.active {
  background-color: rgba(59, 130, 246, 0.2);
}

.new-messagerie-conversation-avatar {
  position: relative;
  margin-right: 0.75rem;
}

.new-messagerie-conversation-avatar img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid white;
}

.dark .new-messagerie-conversation-avatar img {
  border-color: var(--dark-bg);
}

.new-messagerie-conversation-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--success-color);
  border: 2px solid white;
}

.dark .new-messagerie-conversation-status {
  border-color: var(--dark-bg);
}

.new-messagerie-conversation-status.offline {
  background-color: var(--text-secondary);
}

.new-messagerie-conversation-info {
  flex: 1;
  min-width: 0;
}

.new-messagerie-conversation-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  font-size: 0.9375rem;
}

.dark .new-messagerie-conversation-name {
  color: var(--dark-text);
}

.new-messagerie-conversation-preview {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.dark .new-messagerie-conversation-preview {
  color: var(--dark-text-secondary);
}

.new-messagerie-conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: 0.5rem;
}

.new-messagerie-conversation-time {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.dark .new-messagerie-conversation-time {
  color: var(--dark-text-secondary);
}

.new-messagerie-conversation-badge {
  background-color: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}

/* Zone de chat */
.new-messagerie-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.dark .new-messagerie-chat {
  background-color: var(--dark-bg);
}

/* En-tête du chat */
.new-messagerie-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background-color: white;
}

.dark .new-messagerie-chat-header {
  background-color: var(--dark-bg);
  border-color: var(--dark-border);
}

.new-messagerie-chat-user {
  display: flex;
  align-items: center;
}

.new-messagerie-chat-avatar {
  position: relative;
  margin-right: 0.75rem;
}

.new-messagerie-chat-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.new-messagerie-chat-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--success-color);
  border: 2px solid white;
}

.dark .new-messagerie-chat-status {
  border-color: var(--dark-bg);
}

.new-messagerie-chat-status.offline {
  background-color: var(--text-secondary);
}

.new-messagerie-chat-info {
  display: flex;
  flex-direction: column;
}

.new-messagerie-chat-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.dark .new-messagerie-chat-name {
  color: var(--dark-text);
}

.new-messagerie-chat-activity {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.dark .new-messagerie-chat-activity {
  color: var(--dark-text-secondary);
}

.new-messagerie-chat-actions {
  display: flex;
  gap: 0.75rem;
}

.new-messagerie-chat-action {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  color: var(--primary-color);
  transition: all 0.2s ease;
}

.new-messagerie-chat-action:hover {
  background-color: var(--secondary-color);
}

.dark .new-messagerie-chat-action:hover {
  background-color: var(--dark-secondary);
}

/* Zone des messages */
.new-messagerie-messages {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.dark .new-messagerie-messages {
  background-color: #0f172a;
}

/* Styles de message supprimés et remplacés par des classes Tailwind dans les composants JSX */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Styles de base pour les messages supprimés et remplacés par Tailwind */

/* Zone de saisie */
.new-messagerie-input {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: white;
}

.dark .new-messagerie-input {
  background-color: var(--dark-bg);
  border-color: var(--dark-border);
}

.new-messagerie-input-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.new-messagerie-input-actions {
  display: flex;
  gap: 0.5rem;
}

.new-messagerie-input-action {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  color: var(--primary-color);
  transition: all 0.2s ease;
}

.new-messagerie-input-action:hover {
  background-color: var(--secondary-color);
}

.dark .new-messagerie-input-action:hover {
  background-color: var(--dark-secondary);
}

.new-messagerie-input-field {
  flex: 1;
  position: relative;
}

.new-messagerie-input-field input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 9999px;
  border: 1px solid var(--border-color);
  background-color: var(--secondary-color);
  font-size: 0.9375rem;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.new-messagerie-input-field input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.dark .new-messagerie-input-field input {
  background-color: var(--dark-secondary);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

.dark .new-messagerie-input-field input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.new-messagerie-send-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-messagerie-send-btn:hover {
  background-color: var(--primary-hover);
  transform: scale(1.05);
}

.new-messagerie-send-btn:disabled {
  background-color: var(--text-secondary);
  cursor: not-allowed;
  transform: none;
}

.dark .new-messagerie-send-btn:disabled {
  background-color: var(--dark-text-secondary);
}

/* État vide */
.new-messagerie-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
}

.new-messagerie-empty-icon {
  width: 80px;
  height: 80px;
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

.dark .new-messagerie-empty-icon {
  background-color: rgba(59, 130, 246, 0.2);
}

.new-messagerie-empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.dark .new-messagerie-empty-title {
  color: var(--dark-text);
}

.new-messagerie-empty-text {
  font-size: 0.9375rem;
  color: var(--text-secondary);
  max-width: 300px;
}

.dark .new-messagerie-empty-text {
  color: var(--dark-text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
  .new-messagerie {
    flex-direction: column;
  }

  .new-messagerie-conversations {
    width: 100%;
    height: 40%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .dark .new-messagerie-conversations {
    border-color: var(--dark-border);
  }

  .new-messagerie-chat {
    height: 60%;
  }
}
