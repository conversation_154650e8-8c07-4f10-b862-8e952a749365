@startuml Action Delete Sequence Design

title Diagramme de Séquence - Suppression d'une action

actor Administrateur
participant "«View»\nActionForm.jsx" as View
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nAction" as Model

Administrateur -> View : Cliquer sur "Supprimer" pour une action
View -> View : Récupérer l'ID de l'action
View --> Administrateur : Demander confirmation

Administrateur -> View : Confirmer la suppression
View -> Controller : DELETE /api/quiz/quiz-action-by-id/{id}
note right: Route définie dans QuizController.php ligne 1565
Controller -> Model : Récupérer l'action par ID
Model --> Controller : Action trouvée
Controller -> Model : Supprimer l'action
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Affiche<PERSON> le résultat

@enduml
