@startuml Sprint 3 - Diagramme 1A : Sélection et Évaluation des Compétences

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F
participant "Système" as S

== Sélection et Évaluation d'un Cours d'Apprenant ==

group Sélectionner un cours d'un apprenant
    F -> S : Accéder à la liste des apprenants
    S --> F : Afficher la liste des apprenants
    F -> S : Sélectionner un apprenant
    S --> F : Afficher les cours de l'apprenant
    F -> S : Choisir un cours à évaluer
    S --> F : Afficher les détails du cours (quiz, compétences, actions)

    note right of S : Pour valider l'évaluation, il faudra :\n- Évaluer toutes les compétences\n- Cocher toutes les actions
end

group Évaluation des compétences
    F -> S : Consulter les compétences du quiz
    S --> F : Afficher les compétences avec leurs statuts possibles

    loop Pour chaque compétence
        F -> S : Attribuer un statut à la compétence

        alt Compétence acquise
            F -> S : Marquer comme "Acquise"
            S --> F : Mettre à jour le statut en vert

        else Compétence à améliorer
            F -> S : Marquer comme "À améliorer"
            S --> F : Mettre à jour le statut en orange
            F -> S : Cocher les sous-compétences maîtrisées
            S --> F : Enregistrer les sous-compétences cochées

        else Compétence non acquise
            F -> S : Marquer comme "Non acquise"
            S --> F : Mettre à jour le statut en rouge
        end

        S -> S : Sauvegarder le statut de la compétence
    end
end

@enduml
