@startuml Competence Create System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Ajout d'une compétence"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Sélectionner un cours
System --> Admin : Afficher les détails du cours

Admin -> System : Sélectionner un quiz
System --> Admin : Afficher les détails du quiz avec ses compétences

Admin -> System : Cliquer sur "Ajouter une compétence"
System --> Admin : Afficher le formulaire d'ajout de compétence

Admin -> System : Remplir le formulaire (nom_fr, nom_en, categorie_fr, categorie_en)
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Enregistrer la compétence dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des compétences avec la nouvelle compétence
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

@enduml
