@startuml Sprint 3 - Diagramme 4 : Création et Gestion des Événements d'Évaluation - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Administrateur" as Admin

participant "«View»\nCalendarPageView" as CPV

participant "«Controller»\nEvenementController" as EVC

participant "«Model»\nEvenement" as EveM

== Consultation du calendrier des évaluations ==

Admin -> CPV : Accéder au calendrier des évaluations
CPV -> EVC : list(request)
EVC -> EveM : findAll()
EveM --> EVC : evenements[]
EVC --> CPV : JsonResponse avec liste des événements
CPV --> Admin : afficher calendrier

note right of CPV : Calendrier avec événements\nexistants et navigation\npar mois/semaine

== Création d'événement d'évaluation par l'administrateur ==

Admin -> CPV : Cliquer sur "Créer une évaluation"
CPV -> CPV : Afficher formulaire de création
Admin -> CPV : Saisir détails de l'événement (titre, description, date)

note right of Admin : Saisie des informations :\n- Titre de l'évaluation\n- Description\n- Date et heure\n- Type d'événement

CPV -> EVC : create(request)
EVC -> EveM : new Evenement()
EveM -> EveM : setTitre(titre)
EveM -> EveM : setDescription(description)
EveM -> EveM : setDateDebut(dateDebut)
EveM -> EveM : setCategorie("evaluation")
EveM -> EveM : setCouleur("#EA4335")
EVC -> EveM : persist(evenement)

EVC -> EVC : flush()
EVC --> CPV : JsonResponse événement créé avec succès
CPV --> Admin : afficher confirmation et mettre à jour calendrier

note right of CPV : Calendrier mis à jour\navec le nouvel événement

@enduml
