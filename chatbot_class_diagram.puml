@startuml Chatbot Class Diagram

' Définition des classes principales
package "«View»" {
  class ChatbotView {
    -messages: Array
    -inputMessage: string
    -loading: Boolean
    -error: string
    +handleSubmit(): void
    +handleChange(): void
    +handleClearChat(): void
    +scrollToBottom(): void
  }
}

package "«Controller»" {
  class ChatbotController {
    -entityManager: EntityManagerInterface
    -chatbotService: ChatbotService
    -chatbotRepository: ChatbotRepository
    -utilisateurRepository: UtilisateurRepository
    +sendMessage(request: Request): JsonResponse
    +getConversationHistory(apprenantId: int): JsonResponse
    +clearConversation(apprenantId: int): JsonResponse
    +logConversation(apprenantId: int, message: string, response: string): void
  }
}

package "«Service»" {
  class ChatbotService {
    -ollamaClient: OllamaClient
    -logger: LoggerInterface
    +sendMessage(message: string): string
    +processResponse(response: string): string
    +handleError(error: Exception): string
  }

  class OllamaClient {
    -baseUrl: string
    -model: string
    -httpClient: HttpClientInterface
    +generateResponse(prompt: string): string
    +streamResponse(prompt: string): Generator
    +getModels(): array
  }

  class ChatbotLogger {
    -logDir: string
    -entityManager: EntityManagerInterface
    +logInteraction(apprenantId: int, message: string, response: string): void
    +writeToFile(apprenantId: int, message: string, response: string): void
    +saveToDatabase(apprenantId: int, message: string, response: string): void
  }
}

package "«Model»" {
  class ChatbotConversation {
    -id: int
    -message: string
    -response: string
    -timestamp: DateTime
    -apprenant: Utilisateur
    +getId(): int
    +getMessage(): string
    +setMessage(message: string): self
    +getResponse(): string
    +setResponse(response: string): self
    +getTimestamp(): DateTime
    +setTimestamp(timestamp: DateTime): self
    +getApprenant(): Utilisateur
    +setApprenant(apprenant: Utilisateur): self
  }

  class Utilisateur {
    -id: int
    -email: string
    -roles: array
    -chatbotConversations: Collection<ChatbotConversation>
    +getId(): int
    +getEmail(): string
    +getRoles(): array
    +getChatbotConversations(): Collection<ChatbotConversation>
    +addChatbotConversation(conversation: ChatbotConversation): self
    +removeChatbotConversation(conversation: ChatbotConversation): self
  }
}

' Relations entre les classes
ChatbotConversation "0..*" -- "1" Utilisateur : apprenant
ChatbotController ..> ChatbotService
ChatbotController ..> ChatbotLogger
ChatbotService ..> OllamaClient
ChatbotView ..> ChatbotController

@enduml
