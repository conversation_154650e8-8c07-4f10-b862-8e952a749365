@startuml Competence Modify System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Modification d'une compétence"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Sélectionner un cours
System --> Admin : Afficher les détails du cours

Admin -> System : Sélectionner un quiz
System --> Admin : Afficher les détails du quiz avec ses compétences

Admin -> System : Cliquer sur "Modifier" pour une compétence
System --> Admin : Afficher le formulaire de modification pré-rempli

Admin -> System : Modifier les champs de la compétence
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Mettre à jour la compétence dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des compétences avec les modifications
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

@enduml
