@startuml Course Quiz Class Diagram Simple

' Définition des classes principales
package "«View»" {
  class CoursView {
    -cours: Array
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleEdit(): void
  }

  class QuizView {
    -quizzes: Array
    -selectedQuiz: Object
    -loading: Boolean
    +handleSubmit(): void
    +handleImportExcel(): void
  }

  class CompetenceView {
    -competences: Array
    -loading: Boolean
    +handleSubmit(): void
    +handleDelete(): void
  }

  class ActionView {
    -actions: Array
    -loading: Boolean
    +handleSubmit(): void
    +handleDelete(): void
  }
}

package "«Controller»" {
  class CourseController {
    -entityManager: EntityManagerInterface
    -coursRepository: CoursRepository
    +list(): JsonResponse
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
  }

  class QuizController {
    -entityManager: EntityManagerInterface
    -coursRepository: CoursRepository
    +list(): JsonResponse
    +createBatch(request: Request): JsonResponse
    +updateQuizActionById(id: int, request: Request): JsonResponse
    +deleteQuizActionById(id: int): JsonResponse
  }

  class CompetenceController {
    -entityManager: EntityManagerInterface
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
  }

  class ActionController {
    -entityManager: EntityManagerInterface
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
  }
}

package "«Model»" {
  class Cours {
    -id: int
    -titre: string
    -description: string
    -quizzes: Collection<Quiz>
  }

  class Quiz {
    -id: int
    -IDModule: string
    -type: string
    -category: string
    -mainSurface: boolean
    -main: int
    -surface: int
    -nomFR: string
    -nomEN: string
    -cours: Cours
    -competences: Collection<Competence>
    -actions: Collection<Action>
  }

  class Competence {
    -id: int
    -idmodule: string
    -nom_fr: string
    -nom_en: string
    -categorie_fr: string
    -categorie_en: string
    -quiz: Quiz
    -sousCompetences: Collection<SousCompetence>
  }

  class SousCompetence {
    -id: int
    -nom_fr: string
    -nom_en: string
    -competence: Competence
  }

  class Action {
    -id: int
    -idmodule: string
    -nom_fr: string
    -nom_en: string
    -categorie_fr: string
    -categorie_en: string
    -quiz: Quiz
  }
}

' Relations entre les classes
Cours "1" *-- "0..*" Quiz
Quiz "1" *-- "0..*" Competence
Quiz "1" *-- "0..*" Action
Competence "1" *-- "0..*" SousCompetence

' Relations entre les vues et les contrôleurs
CoursView ..> CourseController
QuizView ..> QuizController
CompetenceView ..> CompetenceController
ActionView ..> ActionController

' Relations entre les contrôleurs et les modèles
CourseController ..> Cours
QuizController ..> Quiz
QuizController ..> Action
CompetenceController ..> Competence
ActionController ..> Action

@enduml
