framework:
  messenger:
    failure_transport: failed

    transports:
      # https://symfony.com/doc/current/messenger.html#transport-configuration
      async:
        dsn: "%env(MESSENGER_TRANSPORT_DSN)%"
        options:
          use_notify: true
          check_delayed_interval: 60000
        retry_strategy:
          max_retries: 3
          multiplier: 2
      failed: "doctrine://default?queue_name=failed"
      # sync: 'sync://'

    default_bus: messenger.bus.default

    buses:
      messenger.bus.default: []

    routing:
      # Commenté pour envoyer les emails de manière synchrone (immédiate)
      # Symfony\Component\Mailer\Messenger\SendEmailMessage: async
      Symfony\Component\Notifier\Message\ChatMessage: async
      Symfony\Component\Notifier\Message\SmsMessage: async

      # Route your messages to the transports
      # 'App\Message\YourMessage': async
