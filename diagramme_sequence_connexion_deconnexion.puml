@startuml
title Diagramme de séquence système - Connexion/Déconnexion

actor Utilisateur
participant Système

== Se connecter à la plateforme ==

ref over Utilisate<PERSON>, Système
    Se connecter à la plateforme
end ref

Utilisateur -> Système : Accéder à la page de connexion
Système --> Utilisateur : Afficher le formulaire de connexion
Utilisateur -> Système : Saisir email et mot de passe
Utilisateur -> Système : Cliquer sur "Se connecter"
Système -> Système : Vérifier les identifiants

alt [Identifiants invalides]
    Système --> Utilisateur : Afficher "Identifiants invalides"
else [Identifiants valides]
    alt [Compte non approuvé]
        Système --> Utilisateur : Afficher "Votre compte est en attente d'approbation"
    else [Compte approuvé]
        Système -> Système : Enregistrer l'utilisateur
    end
end

== Se déconnecter de la plateforme ==

ref over Utilisateur, Système
    Se déconnecter de la plateforme
end ref

Utilisateur -> Système : Cliquer sur "Déconnexion"
Système -> Système : Terminer la session utilisateur
Système --> Utilisateur : Rediriger vers la page de connexion

@enduml
