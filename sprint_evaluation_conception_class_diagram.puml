@startuml Sprint 3 - Diagramme de Classes de Conception (Évaluation et Suivi)

skinparam class {
    BackgroundColor white
    BorderColor black
    FontSize 10
    AttributeFontSize 9
    MethodFontSize 9
}

skinparam arrow {
    Color black
    Thickness 1
}

' Vues Frontend
class ApprenantCoursView <<View>> {
    - cours: Array
    - selectedApprenant: Object
    - loadingCourses: Boolean
    --
    + handleSelectApprenant(): void
    + handleAssignCourse(): void
}

class DashboardView <<View>> {
    - courseProgress: Array
    - loading: Boolean
    --
    + displayProgression(): void
    + handleRefresh(): void
}

class CourseDetailsView <<View>> {
    - quizCompetences: Object
    - checkedActions: Object
    - evaluationDetails: Object
    --
    + handleActionCheck(): void
    + handleEvaluationSubmit(): void
}

class CertificateDisplayView <<View>> {
    - certificateData: Object
    - saving: Boolean
    --
    + handleDownload(): void
    + handleSave(): void
}

class FormateurDashboardView <<View>> {
    - evaluationStats: Object
    - loading: Boolean
    --
    + displayStats(): void
    + handleRefresh(): void
}

class CalendarPageView <<View>> {
    - events: Array
    - formData: Object
    --
    + handleEventCreate(): void
    + handleEventSubmit(): void
}

' Contrôleurs Backend
class EvaluationController <<Controller>> {
    - entityManager: EntityManagerInterface
    - evaluationRepository: EvaluationRepository
    - quizRepository: QuizRepository
    - apprenantRepository: ApprenantRepository
    - formateurRepository: FormateurRepository
    --
    + create(request: Request): JsonResponse
    + getEvaluationByQuizAndApprenant(quizId: int, apprenantId: int): JsonResponse
}

class ProgressionController <<Controller>> {
    - entityManager: EntityManagerInterface
    - progressionRepository: ProgressionRepository
    - apprenantRepository: ApprenantRepository
    - coursRepository: CoursRepository
    --
    + getProgressionByApprenant(apprenantId: int): JsonResponse
    + getProgressionByApprenantAndCours(apprenantId: int, coursId: int): JsonResponse
}

class CertificatController <<Controller>> {
    - entityManager: EntityManagerInterface
    - certificatRepository: CertificatRepository
    - apprenantRepository: ApprenantRepository
    - coursRepository: CoursRepository
    --
    + list(): JsonResponse
    + generate(request: Request): JsonResponse
    + download(id: int): Response
    + getCertificatsByApprenant(apprenantId: int): JsonResponse
}

class EvaluationDetailController <<Controller>> {
    - entityManager: EntityManagerInterface
    - evaluationDetailRepository: EvaluationDetailRepository
    --
    + create(request: Request): JsonResponse
    + getByQuizAndApprenant(quizId: int, apprenantId: int): JsonResponse
}

class EvenementController <<Controller>> {
    - entityManager: EntityManagerInterface
    - evenementRepository: EvenementRepository
    - administrateurRepository: AdministrateurRepository
    - apprenantRepository: ApprenantRepository
    - formateurRepository: FormateurRepository
    - webSocketNotificationService: WebSocketNotificationService
    --
    + list(request: Request): JsonResponse
    + create(request: Request): JsonResponse
    + update(id: int, request: Request): JsonResponse
    + delete(id: int): JsonResponse
    + getByDateRange(request: Request): JsonResponse
}

' Entités Modèle
class Evaluation <<Model>> {
    - id: int
    - statutEvaluation: string
    - idmodule: string
    - createdAt: DateTime
    --
    + getStatutEvaluation(): string
    + setStatutEvaluation(statut: string): self
}

class EvaluationDetail <<Model>> {
    - id: int
    - competenceStatuses: Array
    - checkedSousCompetences: Array
    - checkedActions: Array
    - mainValue: float
    - surfaceValue: float
    --
    + getCompetenceStatuses(): Array
    + getCheckedActions(): Array
}

class Progression <<Model>> {
    - id: int
    - tableEvaluations: Array
    --
    + getTableEvaluations(): Array
    + setTableEvaluations(evaluations: Array): self
}

class Certificat <<Model>> {
    - id: int
    - dateObtention: Date
    - contenu: string
    - isAutoGenerated: boolean
    --
    + getDateObtention(): Date
    + getContenu(): string
    + isAutoGenerated(): boolean
}

class Apprenant <<Model>> {
    - id: int
    - name: string
    - email: string
    --
    + getId(): int
    + getName(): string
    + getEmail(): string
}

class Formateur <<Model>> {
    - id: int
    - name: string
    - email: string
    --
    + getId(): int
    + getName(): string
}

class Administrateur <<Model>> {
    - id: int
    - name: string
    - email: string
    --
    + getId(): int
    + getName(): string
}

class Cours <<Model>> {
    - id: int
    - titre: string
    - description: string
    --
    + getId(): int
    + getTitre(): string
}

class Quiz <<Model>> {
    - id: int
    - nomFr: string
    - idModule: string
    - category: string
    - mainSurface: boolean
    --
    + getId(): int
    + getIDModule(): string
    + getNomFr(): string
}

class Evenement <<Model>> {
    - id: int
    - titre: string
    - description: string
    - dateDebut: DateTime
    - dateFin: DateTime
    - journeeEntiere: boolean
    - categorie: string
    - couleur: string
    --
    + getId(): int
    + getTitre(): string
    + getDateDebut(): DateTime
    + getCategorie(): string
    + isJourneeEntiere(): boolean
}

class Notification <<Model>> {
    - id: int
    - description: string
    - read: boolean
    - createdAt: DateTimeImmutable
    - type: string
    --
    + getId(): int
    + getDescription(): string
    + isRead(): boolean
    + getType(): string
}

' Relations Vues -> Contrôleurs
ApprenantCoursView --> ProgressionController : utilise
DashboardView --> ProgressionController : utilise
CourseDetailsView --> EvaluationController : utilise
CourseDetailsView --> EvaluationDetailController : utilise
CertificateDisplayView --> CertificatController : utilise
FormateurDashboardView --> EvaluationController : utilise
CalendarPageView --> EvenementController : utilise

' Relations Contrôleurs -> Entités
EvaluationController --> Evaluation : manipule
EvaluationController --> Apprenant : manipule
EvaluationController --> Formateur : manipule
EvaluationController --> Quiz : manipule

EvaluationDetailController --> EvaluationDetail : manipule
EvaluationDetailController --> Evaluation : manipule

ProgressionController --> Progression : manipule
ProgressionController --> Apprenant : manipule
ProgressionController --> Cours : manipule

CertificatController --> Certificat : manipule
CertificatController --> Progression : manipule
CertificatController --> Apprenant : manipule

EvenementController --> Evenement : manipule
EvenementController --> Administrateur : manipule
EvenementController --> Notification : manipule

' Relations entre entités
Evaluation "1" --> "0..1" Apprenant
Evaluation "*" --> "1" Formateur
Evaluation "*" --> "1" Quiz
Evaluation "1" --> "*" EvaluationDetail

Progression "*" --> "0..1" Apprenant
Progression "*" --> "1" Cours
Progression "*" --> "1" Evaluation

Certificat "*" --> "1" Apprenant
Certificat "0..1" --> "1" Progression

Quiz "*" --> "1" Cours

Evenement "*" --> "*" Administrateur
Evenement "1" --> "*" Notification

Notification "*" --> "0..1" Evaluation
Notification "*" --> "0..1" Evenement
Notification "*" --> "1" Utilisateur

Apprenant "*" --> "*" Cours
Administrateur "*" --> "*" Apprenant

@enduml
