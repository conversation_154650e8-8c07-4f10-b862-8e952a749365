@startuml Course Add Sequence Design

title Diagramme de Séquence - Ajout d'un cours

actor Utilisateur
participant "«View»\nCourseManagementPage.jsx" as View
participant "«Controller»\nCourseContext" as Context
participant "«Controller»\nCourseController" as Controller
participant "«Model»\nCours" as Model

ref over Utilisa<PERSON><PERSON>, Model
  Ajouter un cours
end ref

Utilisateur -> View : Cliquer sur "Ajouter un cours"
View --> Utilisateur : Afficher le formulaire d'ajout de cours

Utilisateur -> View : Remplir le formulaire (titre, description)
Utilisateur -> View : Cliquer sur "Enregistrer"
View -> View : Valider les données du formulaire
View -> Context : addCourse(courseData)

Context -> Controller : POST /api/cours
Controller -> Model : Valider les données
Model -> Model : create(courseData)
Model -> Model : persist(cours)
Model --> Controller : response

alt [success: true]
    Controller --> Context : {success: true, course: data}
    Context --> View : Mettre à jour la liste des cours
    View --> Utilisateur : Afficher "Cours ajouté avec succès"
else [success: false]
    Controller --> Context : {success: false, error: messages}
    Context --> View : Transmettre les erreurs
    View --> Utilisateur : Afficher les erreurs
end

@enduml
