@startuml Action Update Sequence Design

title Diagramme de Séquence - Modification d'une action

actor Administrateur
participant "«View»\nActionForm.jsx" as View
participant "«Controller»\nActionController" as Controller
participant "«Model»\nAction" as Model

Administrateur -> View : Cliquer sur "Modifier" pour une action
View -> Controller : GET /api/action/{id}
Controller -> Model : Récupérer l'action
Model --> Controller : Donn<PERSON> de l'action
Controller --> View : Afficher le formulaire pré-rempli

Administrateur -> View : Modifier les champs (nom_fr, nom_en, categorie_fr, categorie_en)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : PUT /api/action/{id}
Controller -> Model : Mettre à jour l'action
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
