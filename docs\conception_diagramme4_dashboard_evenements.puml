@startuml Diagramme de Séquence - Dashboard des Évaluations et Gestion des Événements

participant "Formateur" as F
participant "Administrateur" as Admin
participant "Apprenant" as A
participant "«View»\nDashboardPage" as V
participant "«View»\nCalendarPage" as VC
participant "«Controller»\nDashboardController" as DC
participant "«Controller»\nEvenementController" as EC

participant "«Model»\nEvaluation" as ME
participant "«Model»\nEvenement" as MEV

== Dashboard des évaluations ==

alt Formateur
    F -> V : Accéder au dashboard
    activate V
    V -> DC : getFormateurStats()
    activate DC
    DC -> ME : findByFormateur(formateurId)
    activate ME
    ME --> DC : evaluations
    deactivate ME
    DC -> DC : calculateFormateurStats()
    DC --> V : JsonResponse(formateurStats)
    deactivate DC
    V --> F : afficher le dashboard de ses évaluations
    deactivate V

else Administrateur
    Admin -> V : Accéder au dashboard global
    activate V
    V -> DC : getStats()
    activate DC
    DC -> ME : findAll()
    activate ME
    ME --> DC : allEvaluations
    deactivate ME
    DC -> DC : calculateGlobalStats()
    DC --> V : JsonResponse(globalStats)
    deactivate DC
    V --> Admin : afficher toutes les statistiques d'évaluation
    deactivate V
end

== Création d'événement d'évaluation ==

Admin -> VC : Accéder au calendrier des événements
activate VC
VC -> EC : list()
activate EC
EC -> MEV : findAll()
activate MEV
MEV --> EC : evenements
deactivate MEV
EC --> VC : JsonResponse(evenements)
deactivate EC
VC --> Admin : afficher le calendrier
deactivate VC

Admin -> VC : Créer un nouvel événement
activate VC
VC --> Admin : afficher le formulaire de création
Admin -> VC : Saisir les détails de l'événement
Admin -> VC : Valider la création
VC -> EC : create(evenementData)
activate EC
EC -> MEV : setTitre(titre)
activate MEV
EC -> MEV : setDescription(description)
EC -> MEV : setDateDebut(dateDebut)
EC -> MEV : setDateFin(dateFin)
MEV -> MEV : persist()
MEV --> EC : evenement
deactivate MEV

note over EC : Les événements sont créés et stockés\nLes notifications peuvent être gérées séparément

EC --> VC : JsonResponse(evenement)
deactivate EC
VC --> Admin : confirmer la création de l'événement
deactivate VC

@enduml
