// Script de diagnostic pour le chatbot
// À exécuter dans la console du navigateur

console.log("🔍 === DIAGNOSTIC CHATBOT ===");

// 1. Vérifier les variables d'environnement
console.log("📋 Variables d'environnement:");
console.log("- VITE_API_URL:", import.meta.env.VITE_API_URL);

// 2. Vérifier le localStorage
console.log("\n📋 LocalStorage:");
console.log("- Token:", localStorage.getItem("token") ? "Présent" : "Absent");
console.log("- User:", localStorage.getItem("user") ? "Présent" : "Absent");

if (localStorage.getItem("user")) {
  try {
    const user = JSON.parse(localStorage.getItem("user"));
    console.log("- User ID:", user.id);
    console.log("- User Email:", user.email);
    console.log("- User Role:", user.role);
  } catch (e) {
    console.error("- Erreur parsing user:", e);
  }
}

// 3. Test de connectivité basique
async function testBasicConnectivity() {
  console.log("\n🌐 Test de connectivité basique:");
  
  try {
    const response = await fetch(`${import.meta.env.VITE_API_URL}/chatbot/test-ollama`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log("- Status:", response.status);
    console.log("- Status Text:", response.statusText);
    console.log("- Headers:", Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log("- Response Data:", data);
    } else {
      const text = await response.text();
      console.log("- Error Response:", text);
    }
  } catch (error) {
    console.error("- Erreur de connectivité:", error);
    console.error("- Error name:", error.name);
    console.error("- Error message:", error.message);
  }
}

// 4. Test avec authentification
async function testWithAuth() {
  console.log("\n🔐 Test avec authentification:");
  
  const token = localStorage.getItem("token");
  if (!token) {
    console.log("- Pas de token disponible");
    return;
  }
  
  try {
    const response = await fetch(`${import.meta.env.VITE_API_URL}/chatbot/test`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log("- Status:", response.status);
    console.log("- Status Text:", response.statusText);
    
    if (response.ok) {
      const data = await response.json();
      console.log("- Response Data:", data);
    } else {
      const text = await response.text();
      console.log("- Error Response:", text);
    }
  } catch (error) {
    console.error("- Erreur avec auth:", error);
  }
}

// 5. Test d'envoi de message
async function testSendMessage() {
  console.log("\n💬 Test d'envoi de message:");
  
  const token = localStorage.getItem("token");
  const user = localStorage.getItem("user");
  
  if (!token || !user) {
    console.log("- Token ou user manquant");
    return;
  }
  
  try {
    const userData = JSON.parse(user);
    
    const response = await fetch(`${import.meta.env.VITE_API_URL}/chatbot/message-extended`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: "Test de diagnostic",
        context: "diagnostic",
        userId: userData.id
      })
    });
    
    console.log("- Status:", response.status);
    console.log("- Status Text:", response.statusText);
    
    if (response.ok) {
      const data = await response.json();
      console.log("- Response Data:", data);
    } else {
      const text = await response.text();
      console.log("- Error Response:", text);
    }
  } catch (error) {
    console.error("- Erreur envoi message:", error);
  }
}

// 6. Vérifier Ollama directement
async function testOllamaDirect() {
  console.log("\n🤖 Test Ollama direct:");
  
  try {
    const response = await fetch("http://127.0.0.1:11434/api/tags", {
      method: 'GET'
    });
    
    console.log("- Status:", response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log("- Ollama Models:", data.models?.length || 0);
    }
  } catch (error) {
    console.error("- Erreur Ollama direct:", error);
  }
}

// Exécuter tous les tests
async function runAllTests() {
  await testBasicConnectivity();
  await testWithAuth();
  await testSendMessage();
  await testOllamaDirect();
  
  console.log("\n✅ === DIAGNOSTIC TERMINÉ ===");
  console.log("Copiez ces résultats pour analyse.");
}

// Lancer le diagnostic
runAllTests();
