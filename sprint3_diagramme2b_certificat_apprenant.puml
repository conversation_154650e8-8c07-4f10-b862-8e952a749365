@startuml Sprint 3 - Diagramme 2B : Génération et Téléchargement de Certificat par l'Apprenant - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Apprenant" as A

participant "«View»\nCertificateDisplayView" as CDV

participant "«Controller»\nCertificatController" as CC

participant "«Model»\nApprenant" as AM
participant "«Model»\nCours" as CoM
participant "«Model»\nProgression" as PM
participant "«Model»\nCertificat" as CM

== Génération de certificat par l'apprenant ==

A -> CDV : Cliquer sur "Générer certificat"
CDV -> CC : generateDirect(Request $request)
CC -> CC : json_decode($request->getContent(), true)

CC -> AM : find($data['apprenantId'])
AM --> CC : $apprenant
CC -> CoM : find($data['coursId'])
CoM --> CC : $cours

CC -> CC : Vérifier certificat existant avec requête SQL

alt Certificat existe déjà
    CC --> CDV : JsonResponse "Certificate already exists" avec données complètes
    CDV --> A : afficher certificat existant

else Aucun certificat existant
    CC -> CC : Récupérer ou créer progression avec évaluation
    CC -> CC : Créer évaluations par défaut si nécessaire
    CC -> CC : Générer contenu certificat avec compétences

    CC -> CC : Insertion directe SQL avec apprenant_id
    CC -> CC : setIsAutoGenerated(true)
    CC --> CDV : JsonResponse "Certificate generated successfully"
    CDV --> A : afficher nouveau certificat

end

== Téléchargement de certificat en PDF ==

A -> CDV : Cliquer sur "Télécharger PDF"
CDV -> CC : download(int $id)
CC -> CM : find($id)
CM --> CC : $certificat

alt Certificat non trouvé
    CC --> CDV : JsonResponse error "Certificat not found"
    CDV --> A : afficher erreur
else Certificat trouvé
    CC -> CC : Récupérer ou générer contenu JSON du certificat
    CC --> CDV : JsonResponse avec données certificat
    CDV -> CDV : QuizService.downloadCertificatPDF(certificatData)
    CDV -> CDV : Générer PDF côté client avec jsPDF
    CDV --> A : télécharger certificat.pdf

    note right of CDV : La génération PDF se fait\ncôté FRONTEND avec jsPDF,\npas côté backend
end

@enduml
