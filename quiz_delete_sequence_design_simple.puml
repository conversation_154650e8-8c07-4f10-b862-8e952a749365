@startuml Quiz Delete Sequence Design

title Diagramme de Séquence - Suppression d'un quiz

actor Administrateur
participant "«View»\nQuiz.jsx" as View
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model

Administrateur -> View : Cliquer sur "Supprimer" pour un quiz
View --> Administrateur : Demander confirmation

Administrateur -> View : Confirmer la suppression
View -> Controller : DELETE /api/quiz/{IDModule}
Controller -> Model : Supprimer le quiz et ses données associées
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : <PERSON>ffiche<PERSON> le résultat

@enduml
