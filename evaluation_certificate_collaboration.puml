@startuml Evaluation Certificate Collaboration Diagram

' Définition des acteurs et classes
actor Apprenant
actor Formateur
circle EvaluationView
circle CertificateView
circle ProgressionView
circle EvaluationController
circle CertificateController
circle ProgressionController
circle EvaluationEntity
circle CertificatEntity
circle ProgressionEntity
circle PdfGeneratorService

' Connexions entre les acteurs et les vues
Apprenant -- ProgressionView
Apprenant -- CertificateView
Formateur -- EvaluationView

' Connexions entre les vues et les contrôleurs
EvaluationView -- EvaluationController
CertificateView -- CertificateController
ProgressionView -- ProgressionController

' Connexions entre les contrôleurs et les entités/services
EvaluationController -- EvaluationEntity
CertificateController -- CertificatEntity
CertificateController -- PdfGeneratorService
ProgressionController -- ProgressionEntity
ProgressionController -- CertificatEntity

@enduml
