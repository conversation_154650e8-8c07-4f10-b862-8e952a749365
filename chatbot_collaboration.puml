@startuml Chatbot Collaboration Diagram

' Définition des acteurs et classes
actor Apprenant
circle ChatbotView
circle ChatbotController
circle ChatbotService
circle OllamaClient
circle ChatbotLogger
circle ChatbotConversationEntity
circle UtilisateurEntity

' Connexions entre l'acteur et les vues
Apprenant -- ChatbotView

' Connexions entre les vues et les contrôleurs
ChatbotView -- ChatbotController

' Connexions entre les contrôleurs et les services/entités
ChatbotController -- ChatbotService
ChatbotController -- ChatbotLogger
ChatbotController -- ChatbotConversationEntity
ChatbotController -- UtilisateurEntity

' Connexions entre les services
ChatbotService -- OllamaClient
ChatbotLogger -- ChatbotConversationEntity

@enduml
