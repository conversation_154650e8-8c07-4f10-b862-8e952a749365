@startuml Quiz Add Sequence Design

title Diagramme de Séquence - Ajout d'un quiz

actor Administrateur
participant "«View»\nQuiz.jsx" as View
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model

Administrateur -> View : Remplir le formulaire (IDModule, Category, Type, MainSurface)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : POST /api/quiz
Controller -> Model : Enregistrer le quiz
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
