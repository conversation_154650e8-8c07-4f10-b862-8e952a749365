@startuml Sprint4_Conception_Class_Diagram

!theme plain
skinparam backgroundColor #FAFAFA
skinparam class {
    BackgroundColor White
    BorderColor #333333
    ArrowColor #333333
    FontSize 10
}

skinparam package {
    BackgroundColor #E8F4FD
    BorderColor #1976D2
    FontStyle bold
}

title "Sprint 4 - Diagramme de Classes de Conception\nMessagerie et Notifications"

' ===== COUCHE VIEW (Frontend React) =====
package "«View»" #E8F4FD {

    class MessagerieView <<View>> {
        - onlineApprenants: Array
        - allConversations: Array
        - activeConversation: Object
        - activeApprenantId: int
        - messages: Object
        - newMessage: string
        - searchTerm: string
        - showEmojiPicker: boolean
        - selectedFile: File
        - loading: boolean
        - error: string
        --
        + fetchData(): void
        + sendMessage(e: Event): void
        + fetchMessages(formateurId: int, apprenantId: int): void
        + markAsRead(id: int): void
        + addEmoji(emoji: Object): void
        + handleFileSelect(): void
    }

    class FormateurMessagerieView <<View>> {
        - onlineApprenants: Array
        - allConversations: Array
        - activeConversation: Object
        - activeApprenantId: int
        - messages: Object
        - newMessage: string
        - searchTerm: string
        - showEmojiPicker: boolean
        - selectedFile: File
        - loading: boolean
        - error: string
        --
        + fetchData(): void
        + sendMessage(e: Event): void
        + fetchMessages(formateurId: int, apprenantId: int): void
        + markAsRead(id: int): void
        + addEmoji(emoji: Object): void
        + handleFileSelect(): void
    }

    class NotificationCenterView <<View>> {
        - notifications: Array
        - unreadCount: int
        - showNotifications: boolean
        - loading: boolean
        --
        + fetchNotifications(): void
        + markAsRead(id: int): void
        + markAllAsRead(): void
        + handleNotificationClick(notification: Object): void
        + toggleNotifications(): void
    }

    class ApprenantReclamationView <<View>> {
        - reclamations: Array
        - selectedReclamation: Object
        - newReclamation: Object
        - messageLength: int
        - showConfirmDialog: boolean
        - loading: boolean
        --
        + handleSubmit(): void
        + handleSelectReclamation(reclamation: Object): void
        + handleBackToList(): void
        + formatDate(dateString: string): string
    }

    class AdminReclamationView <<View>> {
        - reclamations: Array
        - selectedReclamation: Object
        - filterStatus: string
        - searchTerm: string
        - replyContent: string
        - isGeneratingReply: boolean
        - selectedTemplate: string
        - showTemplates: boolean
        - submitting: boolean
        --
        + handleSelectReclamation(reclamation: Object): void
        + handleStatusChange(id: int, newStatus: string): void
        + handleReplySubmit(e: Event): void
        + handleTemplateSelect(template: Object): void
        + handleGenerateReply(): void
    }

    class QuizDetailsView <<View>> {
        - quiz: Object
        - competences: Array
        - actions: Array
        - checkedActions: Object
        - checkedSousCompetences: Object
        - loading: boolean
        - error: string
        - mainValue: int
        - surfaceValue: int
        - evaluation: string
        - alreadyEvaluated: boolean
        - reevaluationAllowed: boolean
        --
        + handleEvaluation(competenceId: int, evaluation: string): void
        + handleQuizEvaluation(result: string): void
        + checkEvaluationConditions(): Object
        + updateButtonsState(): void
        + handleActionCheck(actionId: int): void
    }

    class CertificateDisplayView <<View>> {
        - saving: boolean
        - certificateData: Object
        - initialCertificateData: Object
        --
        + handleSave(): void
        + handleDownload(): void
        + handleClose(): void
        + generateCertificateContent(): string
    }

    class CalendarPageView <<View>> {
        - events: Array
        - selectedEvent: Object
        - showEventForm: boolean
        - formData: Object
        - currentDate: Date
        - upcomingEvents: Array
        - loading: boolean
        --
        + handleCreateEvent(): void
        + handleUpdateEvent(id: int): void
        + handleDeleteEvent(id: int): void
        + handleEventSelect(event: Object): void
        + handleDateChange(date: Date): void
        + fetchEvents(): void
        + fetchUpcomingEvents(): void
    }
}

' ===== COUCHE CONTROLLER (Backend Symfony) =====
package "«Controller»" #FFF3E0 {

    class MessagerieController <<Controller>> {
        - entityManager: EntityManagerInterface
        - messagerieRepository: MessagerieRepository
        - formateurRepository: FormateurRepository
        - apprenantRepository: ApprenantRepository
        - utilisateurRepository: UtilisateurRepository
        - security: Security
        - serializer: SerializerInterface
        - validator: ValidatorInterface
        --
        + getConversation(formateurId: int, apprenantId: int): JsonResponse
        + getFormateurConversations(formateurId: int): JsonResponse
        + getApprenantConversations(apprenantId: int): JsonResponse
        + formateurEnvoyerMessage(request: Request, formateurId: int, apprenantId: int): JsonResponse
        + apprenantEnvoyerMessage(request: Request, apprenantId: int, formateurId: int): JsonResponse
        + marquerLu(id: int): JsonResponse
        + getFormateursForApprenant(apprenantId: int): JsonResponse
        + getApprenantsForFormateur(formateurId: int): JsonResponse
    }

    class NotificationController <<Controller>> {
        - entityManager: EntityManagerInterface
        - notificationRepository: NotificationRepository
        - security: Security
        - serializer: SerializerInterface
        --
        + getUserNotifications(request: Request): JsonResponse
        + markAsRead(id: int): JsonResponse
        + markAllAsRead(): JsonResponse
        + deleteNotification(id: int): JsonResponse
    }

    class ReclamationController <<Controller>> {
        - entityManager: EntityManagerInterface
        - reclamationRepository: ReclamationRepository
        - utilisateurRepository: UtilisateurRepository
        - security: Security
        - serializer: SerializerInterface
        --
        + list(): JsonResponse
        + getUserReclamations(): JsonResponse
        + show(id: int): JsonResponse
        + create(request: Request): JsonResponse
        + reply(id: int, request: Request): JsonResponse
    }

    class EvaluationController <<Controller>> {
        - entityManager: EntityManagerInterface
        - evaluationRepository: EvaluationRepository
        - quizRepository: QuizRepository
        - apprenantRepository: ApprenantRepository
        - formateurRepository: FormateurRepository
        - progressionRepository: ProgressionRepository
        - certificatRepository: CertificatRepository
        - emailService: EmailService
        - security: Security
        - serializer: SerializerInterface
        --
        + list(): JsonResponse
        + show(id: int): JsonResponse
        + create(request: Request): JsonResponse
        + getEvaluationsByIdmodule(idmodule: string): JsonResponse
        + getEvaluationByIdmoduleAndApprenant(idmodule: string, apprenantId: int): JsonResponse
        + getEvaluationByQuizAndApprenant(quizId: int, apprenantId: int): JsonResponse
        - checkAndGenerateCertificateIfNeeded(apprenant: Apprenant, cours: Cours): array
    }

    class CertificatController <<Controller>> {
        - entityManager: EntityManagerInterface
        - certificatRepository: CertificatRepository
        - apprenantRepository: ApprenantRepository
        - coursRepository: CoursRepository
        - progressionRepository: ProgressionRepository
        - evaluationRepository: EvaluationRepository
        - quizRepository: QuizRepository
        - security: Security
        - serializer: SerializerInterface
        - emailService: EmailService
        --
        + list(): JsonResponse
        + checkAndGenerate(apprenantId: int, coursId: int): JsonResponse
        + show(id: int): JsonResponse
        + generateDirect(request: Request): JsonResponse
        + download(id: int): Response
        + getCertificatsByApprenant(apprenantId: int): JsonResponse
    }

    class EvenementController <<Controller>> {
        - entityManager: EntityManagerInterface
        - evenementRepository: EvenementRepository
        - administrateurRepository: AdministrateurRepository
        - apprenantRepository: ApprenantRepository
        - formateurRepository: FormateurRepository
        - security: Security
        - serializer: SerializerInterface
        - webSocketNotificationService: WebSocketNotificationService
        --
        + debug(): JsonResponse
        + list(request: Request): JsonResponse
        + show(id: int): JsonResponse
        + create(request: Request): JsonResponse
        + update(request: Request, id: int): JsonResponse
        + delete(id: int): JsonResponse
        + getByAdministrateur(id: int): JsonResponse
        + getByDateRange(request: Request): JsonResponse
        + getUpcoming(request: Request): JsonResponse
    }
}

' ===== COUCHE MODEL (Entités Backend) =====
package "«Model»" #E8F5E8 {

    class Messagerie <<Model>> {
        - id: int
        - message: string
        - lu: boolean
        - date: DateTime
        - sentByFormateur: boolean
        - formateur: Formateur
        - apprenant: Apprenant
        --
        + marquerLu(): void
    }

    class Notification <<Model>> {
        - id: int
        - Description: string
        - read: boolean
        - createdAt: DateTimeImmutable
        - type: string
        - user: Utilisateur
        - messagerie: Messagerie
        - reclamation: Reclamation
        - certificat: Certificat
        - evaluation: Evaluation
        - evenement: Evenement
        --
        + markAsRead(): void
    }

    class Reclamation <<Model>> {
        - id: int
        - subject: string
        - message: string
        - status: string
        - date: DateTime
        - response: string
        - responses: array
        - responseDate: DateTime
        - user: Utilisateur
        --
        + addResponse(response: string, adminName: string, date: DateTime): self
    }

    class Formateur <<Model>> {
        - id: int
        - name: string
        - email: string
        - phone: int
        - profileImage: string
        - password: string
        - role: string
        - roles: array
        - isApproved: boolean
        - utilisateur: Utilisateur
    }

    class Apprenant <<Model>> {
        - id: int
        - name: string
        - email: string
        - phone: int
        - profileImage: string
        - password: string
        - role: string
        - roles: array
        - isApproved: boolean
        - utilisateur: Utilisateur
    }

    class Administrateur <<Model>> {
        - id: int
        - name: string
        - email: string
        - phone: int
        - profileImage: string
        - password: string
        - role: string
        - roles: array
        - isApproved: boolean
        - utilisateur: Utilisateur
        --
        + createEvenement(evenement: Evenement): void
        + replyToReclamation(reclamation: Reclamation, response: string): void
    }

    class Evaluation <<Model>> {
        - id: int
        - statutEvaluation: string
        - idmodule: string
        - createdAt: DateTime
        - quiz: Quiz
        - formateur: Formateur
        - apprenant: Apprenant
        --
        + synchronizeIdmodule(): void
        + checkCertificateGeneration(): boolean
    }

    class Certificat <<Model>> {
        - id: int
        - dateObtention: DateTime
        - contenu: string
        - isAutoGenerated: boolean
        - apprenant: Apprenant
        - progression: Progression
        --
        + generateContent(): string
        + downloadPDF(): Response
    }

    class Evenement <<Model>> {
        - id: int
        - titre: string
        - description: string
        - dateDebut: DateTime
        - dateFin: DateTime
        - journeeEntiere: boolean
        - categorie: string
        - couleur: string
        - administrateurs: Collection
        --
        + notifyUsers(): void
        + isUpcoming(): boolean
    }
}

' ===== RELATIONS =====

' Relations Views vers Controllers
MessagerieView --> MessagerieController : utilise
FormateurMessagerieView --> MessagerieController : utilise
NotificationCenterView --> NotificationController : utilise
ApprenantReclamationView --> ReclamationController : utilise
AdminReclamationView --> ReclamationController : utilise
QuizDetailsView --> EvaluationController : utilise
CertificateDisplayView --> CertificatController : utilise
CalendarPageView --> EvenementController : utilise

' Relations Controllers vers Models
MessagerieController --> Messagerie : manipule
MessagerieController --> Formateur : manipule
MessagerieController --> Apprenant : manipule
MessagerieController --> Notification : manipule

NotificationController --> Notification : manipule

ReclamationController --> Reclamation : manipule
ReclamationController --> Notification : manipule
ReclamationController --> Administrateur : manipule

EvaluationController --> Evaluation : manipule
EvaluationController --> Quiz : manipule
EvaluationController --> Formateur : manipule
EvaluationController --> Apprenant : manipule
EvaluationController --> Notification : manipule
EvaluationController --> Certificat : manipule

CertificatController --> Certificat : manipule
CertificatController --> Apprenant : manipule
CertificatController --> Cours : manipule
CertificatController --> Progression : manipule
CertificatController --> Notification : manipule

EvenementController --> Evenement : manipule
EvenementController --> Administrateur : manipule
EvenementController --> Apprenant : manipule
EvenementController --> Formateur : manipule
EvenementController --> Notification : manipule

' Relations entre Models
Messagerie "0..*" --> "1" Formateur : formateur
Messagerie "0..*" --> "1" Apprenant : apprenant
Messagerie "1" --> "0..*" Notification : notifications

Notification "0..*" --> "0..1" Messagerie : messagerie
Notification "0..*" --> "0..1" Reclamation : reclamation
Notification "0..*" --> "0..1" Certificat : certificat
Notification "0..*" --> "0..1" Evaluation : evaluation
Notification "0..*" --> "0..1" Evenement : evenement
Notification "0..*" --> "1" Utilisateur : user

Reclamation "0..*" --> "1" Utilisateur : user
Reclamation "1" --> "0..*" Notification : notifications

Evaluation "0..*" --> "1" Quiz : quiz
Evaluation "0..*" --> "1" Formateur : formateur
Evaluation "0..*" --> "1" Apprenant : apprenant
Evaluation "1" --> "0..*" Notification : notifications

Certificat "0..*" --> "1" Apprenant : apprenant
Certificat "0..*" --> "1" Progression : progression
Certificat "1" --> "0..*" Notification : notifications

Evenement "0..*" --> "0..*" Administrateur : administrateurs
Evenement "1" --> "0..*" Notification : notifications

@enduml
