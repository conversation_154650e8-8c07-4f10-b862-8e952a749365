@startuml Diagramme de Séquence - Gestion des Utilisateurs (Affichage et Ajout)

actor Administrateur
participant "<<View>>\nUsersManagementPage" as View
participant "<<Controller>>\nAdminController" as Controller
participant "<<Model>>\nUtilisateur" as Model

title Diagramme de Séquence - Gestion des Utilisateurs (Affichage et Ajout) - Architecture MVC

ref over Administrateur, Model : Gérer les utilisateurs - Affichage et Ajout

== Afficher les utilisateurs ==

Administrateur -> View : Cliquer sur "Gestion des utilisateurs"
activate View

View -> Controller : getApprovedUsers()
activate Controller

Controller -> Model : findApprovedUsers()
activate Model
Model --> Controller : users
deactivate Model

Controller --> View : users
deactivate Controller

View --> Administrateur : afficher liste des utilisateurs

== Ajouter un utilisateur ==

Administrateur -> View : Cliquer sur "Ajouter un utilisateur"
View --> Administrateur : afficher formulaire d'ajout

Administrateur -> View : Remplir le formulaire et soumettre
activate View

View -> Controller : addUser(userData)
activate Controller

Controller -> Controller : validate(userData)

alt données invalides
    Controller --> View : errors
    View --> Administrateur : afficher les erreurs
else données valides
    Controller -> Model : create(userData)
    activate Model
    Model --> Controller : user
    deactivate Model

    Controller -> Model : save(user)
    activate Model
    Model --> Controller : user
    deactivate Model

    Controller --> View : success
    View --> Administrateur : afficher "Utilisateur ajouté avec succès"
    View --> Administrateur : mettre à jour la liste des utilisateurs
end

deactivate Controller
deactivate View

@enduml
