@startuml Evaluation Certificate Class Diagram

' Définition des classes principales
package "«View»" {
  class EvaluationView {
    -evaluations: Array
    -selectedEvaluation: Object
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleMarkSatisfaisant(): void
    +handleMarkNonSatisfaisant(): void
  }

  class CertificateView {
    -certificates: Array
    -selectedCertificate: Object
    -loading: Boolean
    +handleDownload(): void
    +handleGenerate(): void
    +handleView(): void
  }

  class ProgressionView {
    -progression: Number
    -actions: Array
    -checkedActions: Array
    +handleCheckAction(): void
    +calculateProgression(): void
    +handleGenerateCertificate(): void
  }
}

package "«Controller»" {
  class EvaluationController {
    -entityManager: EntityManagerInterface
    -evaluationRepository: EvaluationRepository
    -validator: ValidatorInterface
    +list(): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(id: int, request: Request): JsonResponse
    +delete(id: int): JsonResponse
    +markSatisfaisant(id: int): JsonResponse
    +markNonSatisfaisant(id: int): JsonResponse
    +getByApprenant(apprenantId: int): JsonResponse
  }

  class CertificateController {
    -entityManager: EntityManagerInterface
    -certificateRepository: CertificateRepository
    -validator: ValidatorInterface
    -pdfGenerator: PdfGeneratorService
    +list(): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +generate(coursId: int, apprenantId: int): JsonResponse
    +download(id: int): Response
    +getByCours(coursId: int): JsonResponse
    +getByApprenant(apprenantId: int): JsonResponse
  }

  class ProgressionController {
    -entityManager: EntityManagerInterface
    -progressionRepository: ProgressionRepository
    -actionRepository: ActionRepository
    -certificateRepository: CertificateRepository
    +getProgression(coursId: int, apprenantId: int): JsonResponse
    +updateActionStatus(actionId: int, apprenantId: int, status: boolean): JsonResponse
    +checkAllActionsCompleted(coursId: int, apprenantId: int): JsonResponse
  }
}

package "«Model»" {
  class Evaluation {
    -id: int
    -status: string
    -date: DateTime
    -apprenant: Utilisateur
    -cours: Cours
    -formateur: Utilisateur
    +getId(): int
    +getStatus(): string
    +setStatus(status: string): self
    +getDate(): DateTime
    +setDate(date: DateTime): self
    +getApprenant(): Utilisateur
    +setApprenant(apprenant: Utilisateur): self
    +getCours(): Cours
    +setCours(cours: Cours): self
    +getFormateur(): Utilisateur
    +setFormateur(formateur: Utilisateur): self
    +isSatisfaisant(): boolean
  }

  class Certificat {
    -id: int
    -date: DateTime
    -path: string
    -apprenant: Utilisateur
    -cours: Cours
    -is_auto_generated: boolean
    +getId(): int
    +getDate(): DateTime
    +setDate(date: DateTime): self
    +getPath(): string
    +setPath(path: string): self
    +getApprenant(): Utilisateur
    +setApprenant(apprenant: Utilisateur): self
    +getCours(): Cours
    +setCours(cours: Cours): self
    +isAutoGenerated(): boolean
    +setIsAutoGenerated(isAutoGenerated: boolean): self
  }

  class Progression {
    -id: int
    -pourcentage: float
    -apprenant: Utilisateur
    -cours: Cours
    -actionsCompleted: Collection<ActionStatus>
    +getId(): int
    +getPourcentage(): float
    +setPourcentage(pourcentage: float): self
    +getApprenant(): Utilisateur
    +setApprenant(apprenant: Utilisateur): self
    +getCours(): Cours
    +setCours(cours: Cours): self
    +getActionsCompleted(): Collection<ActionStatus>
    +addActionCompleted(actionStatus: ActionStatus): self
    +removeActionCompleted(actionStatus: ActionStatus): self
    +calculateProgression(): float
    +isComplete(): boolean
  }

  class ActionStatus {
    -id: int
    -completed: boolean
    -action: Action
    -progression: Progression
    +getId(): int
    +isCompleted(): boolean
    +setCompleted(completed: boolean): self
    +getAction(): Action
    +setAction(action: Action): self
    +getProgression(): Progression
    +setProgression(progression: Progression): self
  }

  class PdfGeneratorService {
    -twig: Environment
    -options: array
    +generateCertificate(certificat: Certificat): string
    +renderTemplate(template: string, data: array): string
    +generatePdf(html: string, outputPath: string): void
  }
}

' Relations entre les classes
Evaluation "0..*" -- "1" Utilisateur : apprenant
Evaluation "0..*" -- "1" Utilisateur : formateur
Evaluation "0..*" -- "1" Cours
Certificat "0..*" -- "1" Utilisateur : apprenant
Certificat "0..*" -- "1" Cours
Progression "0..*" -- "1" Utilisateur : apprenant
Progression "0..*" -- "1" Cours
Progression "1" *-- "0..*" ActionStatus
ActionStatus "0..*" -- "1" Action

' Relations entre les vues et les contrôleurs
EvaluationView ..> EvaluationController
CertificateView ..> CertificateController
ProgressionView ..> ProgressionController

' Relations entre les contrôleurs et les modèles
EvaluationController ..> Evaluation
CertificateController ..> Certificat
CertificateController ..> PdfGeneratorService
ProgressionController ..> Progression
ProgressionController ..> ActionStatus
ProgressionController ..> Certificat

@enduml
