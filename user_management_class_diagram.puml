@startuml User Management Class Diagram

' Définition des classes principales
package "«View»" {
  class RegisterView {
    -formData: Object
    -errorMessage: String
    -successMessage: String
    -imagePreview: String
    -showConfirmPassword: Boolean
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +handleImageChange(): void
    +togglePasswordVisibility(): void
  }

  class LoginView {
    -email: String
    -password: String
    -errorMessage: String
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +togglePasswordVisibility(): void
  }

  class ForgotPasswordView {
    -email: String
    -successMessage: String
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
  }

  class ResetPasswordView {
    -password: String
    -confirmPassword: String
    -successMessage: String
    -loading: Boolean
    +handleSubmit(): void
    +handleChange(): void
    +togglePasswordVisibility(): void
  }

  class UserManagementView {
    -users: Array
    -currentPage: Number
    -loading: Boolean
    -searchTerm: String
    -statusFilter: String
    -currentRole: String
    -dialog: Object
    +getUsers(): void
    +handleSearch(): void
    +handleFilter(): void
    +handlePageChange(): void
    +handleApproveUser(): void
    +handleRejectUser(): void
    +handleDeleteUser(): void
  }
}

package "«Controller»" {
  class AuthController {
    -entityManager: EntityManagerInterface
    -utilisateurRepository: UtilisateurRepository
    -passwordHasher: UserPasswordHasherInterface
    -validator: ValidatorInterface
    -jwtSecret: String
    +register(request: Request): JsonResponse
    +login(request: Request): JsonResponse
    +logout(): JsonResponse
    +updateUserData(id: int, request: Request): Promise
    +updateUserDataByEmail(email: String, request: Request): Promise
    +deleteAccount(): Promise
    +getPendingUsers(): Promise
    +approveUser(id: int, request: Request): JsonResponse
    +rejectUser(id: int, reason: String): Promise
    +forgotPassword(email: String): Promise
    +resetPassword(token: String, password: String): Promise
    +generateJwtToken(user: Utilisateur): String
  }

  class AdminController {
    -entityManager: EntityManagerInterface
    -utilisateurRepository: UtilisateurRepository
    -emailService: EmailService
    +getApprovedUsers(): JsonResponse
    +approveUser(id: int, request: Request): JsonResponse
    +rejectUser(id: int): JsonResponse
    +deleteUser(id: int): JsonResponse
  }
}

package "«Model»" {
  class Utilisateur {
    -id: int
    -name: String
    -email: String
    -phone: String
    -profileImage: String
    -password: String
    -roles: Array
    -isApproved: Boolean
    -resetToken: String
    -resetTokenExpiresAt: DateTime
    +getId(): int
    +getName(): String
    +setName(name: String): self
    +getEmail(): String
    +setEmail(email: String): self
    +getPhone(): String
    +setPhone(phone: String): self
    +getProfileImage(): String
    +setProfileImage(profileImage: String): self
    +getPassword(): String
    +setPassword(password: String): self
    +getRoles(): Array
    +setRoles(roles: Array): self
    +isApproved(): Boolean
    +setApproved(approved: Boolean): self
    +getResetToken(): String
    +setResetToken(token: String): self
    +getResetTokenExpiresAt(): DateTime
    +setResetTokenExpiresAt(expiresAt: DateTime): self
  }

  class JwtAuthenticator {
    -jwtSecret: String
    -utilisateurRepository: UtilisateurRepository
    +supports(request: Request): Boolean
    +onAuthenticationSuccess(request: Request, token: TokenInterface, firewallName: String): Response
    +onAuthenticationFailure(request: Request, exception: AuthenticationException): Response
    +authenticate(request: Request, authException: AuthenticationException): Response
  }

  class EmailService {
    -mailer: MailerInterface
    -sender: String
    +sendEmail(to: String, subject: String, body: String): void
    +sendApprovalEmail(user: Utilisateur): Boolean
    +sendRejectionEmail(user: Utilisateur, reason: String): Boolean
    +sendPasswordResetEmail(user: Utilisateur, resetToken: String): Boolean
  }
}

' Relations entre les classes
RegisterView ..> AuthController
LoginView ..> AuthController
ForgotPasswordView ..> AuthController
ResetPasswordView ..> AuthController
UserManagementView ..> AdminController

AuthController ..> Utilisateur
AuthController ..> JwtAuthenticator
AuthController ..> EmailService
AdminController ..> Utilisateur
AdminController ..> EmailService

@enduml
