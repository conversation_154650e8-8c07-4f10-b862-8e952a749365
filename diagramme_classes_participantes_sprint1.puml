@startuml Diagramme de Classes Participantes - Sprint 1

skinparam classAttributeIconSize 0
skinparam classFontStyle bold
skinparam classFontSize 14
skinparam classBackgroundColor white
skinparam classBorderColor black
skinparam classBorderThickness 2
skinparam classStereotypeFontSize 12
skinparam circledCharacterFontSize 12
skinparam circledCharacterRadius 12
skinparam circledCharacterFontName Arial
skinparam defaultFontName Arial
skinparam arrowThickness 2
skinparam arrowColor black

' Acteur
actor Utilisateur

' Vues Frontend
circle "RegisterView" as RegisterView
circle "LoginView" as LoginView
circle "ForgotPasswordView" as ForgotPasswordView
circle "ResetPasswordView" as ResetPasswordView
circle "UserManagementView" as UserManagementView
circle "RequestsView" as RequestsView

' Contrôleurs Backend
circle "AuthController" as AuthController
circle "AdminController" as AdminController

' Modèles et Services
circle "Utilisateur" as UtilisateurModel
circle "EmailService" as EmailService
circle "JwtAuthenticator" as JwtAuthenticator

' Relations avec l'utilisateur
Utilisateur -- RegisterView
Utilisateur -- LoginView
Utilisateur -- ForgotPasswordView
Utilisateur -- ResetPasswordView
Utilisateur -- UserManagementView
Utilisateur -- RequestsView

' Relations avec AuthController
RegisterView -- AuthController
LoginView -- AuthController
ForgotPasswordView -- AuthController
ResetPasswordView -- AuthController

' Relations avec AdminController
UserManagementView -- AdminController
RequestsView -- AdminController

' Relations avec les modèles et services
AuthController -- UtilisateurModel
AuthController -- JwtAuthenticator
AdminController -- UtilisateurModel
AdminController -- EmailService

@enduml
