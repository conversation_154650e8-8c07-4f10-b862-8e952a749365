@startuml Competence Update Sequence Design

title Diagramme de Séquence - Modification d'une compétence

actor Administrateur
participant "«View»\nCompetenceForm.jsx" as View
participant "«Controller»\nCompetenceController" as Controller
participant "«Model»\nCompetence" as Model

Administrateur -> View : Cliquer sur "Modifier" pour une compétence
View -> Controller : GET /api/competence/{id}
Controller -> Model : Récupérer la compétence
Model --> Controller : Donn<PERSON> de la compétence
Controller --> View : Afficher le formulaire pré-rempli

Administrateur -> View : Modifier les champs (nom_fr, nom_en, categorie_fr, categorie_en)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : PUT /api/competence/{id}
Controller -> Model : Mettre à jour la compétence
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
