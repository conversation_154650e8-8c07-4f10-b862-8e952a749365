@startuml Diagramme de Séquence - Gestion des Utilisateurs (Approbation et Rejet)

actor Administrateur
participant "<<View>>\nRequestsPage" as View
participant "<<Controller>>\nAdminController" as Controller
participant "<<Model>>\nUtilisateur" as Model
participant "<<Model>>\nEmailService" as EmailService

title Diagramme de Séquence - Gestion des Utilisateurs (Approbation et Rejet) - Architecture MVC

ref over Administrateur, EmailService : Gérer les utilisateurs - Approbation et Rejet

== Afficher les demandes d'inscription ==

Administrateur -> View : Cliquer sur "Demandes d'inscription"
activate View

View -> Controller : getPendingUsers()
activate Controller

Controller -> Model : findPendingUsers()
activate Model
Model --> Controller : pendingUsers
deactivate Model

Controller --> View : pendingUsers
deactivate Controller

View --> Administrateur : afficher liste des demandes

== Approuver un utilisateur ==

Administrateur -> View : Cliquer sur "Approuver" pour un utilisateur
View --> Administrateur : afficher boîte de dialogue de confirmation
Administrateur -> View : Confirmer l'approbation
activate View

View -> Controller : approveUser(userId)
activate Controller

Controller -> Model : find(userId)
activate Model
Model --> Controller : user
deactivate Model

Controller -> Model : setApproved(true)
activate Model
Model --> Controller : user
deactivate Model

Controller -> Model : save(user)
activate Model
Model --> Controller : user
deactivate Model

Controller -> EmailService : sendApprovalEmail(user)
activate EmailService
EmailService --> Controller : emailSent
deactivate EmailService

Controller --> View : success
deactivate Controller

View --> Administrateur : afficher "Utilisateur approuvé avec succès"
View --> Administrateur : mettre à jour la liste des demandes

deactivate View

== Rejeter un utilisateur ==

Administrateur -> View : Cliquer sur "Rejeter" pour un utilisateur
View --> Administrateur : afficher boîte de dialogue avec champ de raison
Administrateur -> View : Saisir la raison et confirmer
activate View

View -> Controller : rejectUser(userId, reason)
activate Controller

Controller -> Model : find(userId)
activate Model
Model --> Controller : user
deactivate Model

Controller -> EmailService : sendRejectionEmail(user, reason)
activate EmailService
EmailService --> Controller : emailSent
deactivate EmailService

Controller -> Model : delete(user)
activate Model
Model --> Controller : deleted
deactivate Model

Controller --> View : success
deactivate Controller

View --> Administrateur : afficher "Utilisateur rejeté avec succès"
View --> Administrateur : mettre à jour la liste des demandes

deactivate View

@enduml
