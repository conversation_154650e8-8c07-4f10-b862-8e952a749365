@startuml Diagramme de Séquence - Réinitialisation <PERSON><PERSON> (MVC)

actor Utilisateur
participant "<<View>>\nForgotPassword.jsx" as ForgotView
participant "<<View>>\nResetPassword.jsx" as ResetView
participant "<<Controller>>\nAuthContext" as Controller
participant "<<Controller>>\nAuthController" as BackendController
participant "<<Model>>\nUtilisateur" as Model
participant "<<Service>>\nEmailService" as EmailService

title Diagramme de Séquence - Réinitialisation Mot de <PERSON>e (Architecture MVC)

ref over Utilisateur, EmailService : Réinitialiser le mot de passe

== Demande de réinitialisation ==

Utilisateur -> ForgotView : Cliquer sur "Mot de passe oublié"
activate ForgotView

ForgotView -> ForgotView : Valider l'email
ForgotView -> Controller : forgotPassword(email)
activate Controller

Controller -> BackendController : POST /api/forgot-password
activate BackendController

BackendController -> Model : findByEmail(email)
activate Model
Model --> BackendController : utilisateur
deactivate Model

alt utilisateur existe
    BackendController -> BackendController : generateResetToken()
    BackendController -> Model : setResetToken(token)
    activate Model
    Model --> BackendController : utilisateur
    deactivate Model
    
    BackendController -> EmailService : sendPasswordResetEmail(utilisateur, token)
    activate EmailService
    EmailService --> BackendController : emailSent
    deactivate EmailService
end

BackendController --> Controller : response
deactivate BackendController

Controller --> ForgotView : {success: true}
ForgotView --> Utilisateur : afficher "Un email a été envoyé si l'adresse existe"

deactivate Controller
deactivate ForgotView

== Réinitialisation du mot de passe ==

Utilisateur -> ResetView : Accéder au lien de réinitialisation
activate ResetView

ResetView -> ResetView : Afficher le formulaire de nouveau mot de passe
Utilisateur -> ResetView : Saisir le nouveau mot de passe
Utilisateur -> ResetView : Cliquer sur "Enregistrer"

ResetView -> ResetView : Valider le mot de passe
ResetView -> Controller : resetPassword(token, password)
activate Controller

Controller -> BackendController : POST /api/reset-password/{token}
activate BackendController

BackendController -> Model : findByResetToken(token)
activate Model
Model --> BackendController : utilisateur
deactivate Model

alt token valide et non expiré
    BackendController -> BackendController : hashPassword(password)
    BackendController -> Model : setPassword(hashedPassword)
    activate Model
    Model --> BackendController : utilisateur
    deactivate Model
    
    BackendController -> Model : clearResetToken()
    activate Model
    Model --> BackendController : utilisateur
    deactivate Model
    
    BackendController --> Controller : {success: true}
    Controller --> ResetView : {success: true}
    ResetView --> Utilisateur : afficher "Mot de passe réinitialisé avec succès"
    ResetView --> Utilisateur : rediriger vers la page de connexion
else
    BackendController --> Controller : {success: false, error: message}
    Controller --> ResetView : {success: false, error: message}
    ResetView --> Utilisateur : afficher "Token invalide ou expiré"
end

deactivate BackendController
deactivate Controller
deactivate ResetView

@enduml
