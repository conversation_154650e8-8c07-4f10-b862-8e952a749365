@startuml
title Diagramme de séquence système - Ajouter des utilisateurs

actor Administrateur
participant Système

== Ajouter des utilisateurs ==

ref over Administrate<PERSON>, Système
    Ajouter des utilisateurs
end ref

Administrateur -> Système : Accéder à la page de gestion des utilisateurs
Système -> Système : Récupérer la liste des utilisateurs approuvés
Système --> Administrateur : Afficher la liste des utilisateurs

alt [Ajouter un utilisateur]
    Administrateur -> Système : Cliquer sur "Ajouter un utilisateur"
    Système --> Administrateur : Afficher le formulaire d'ajout
    Administrateur -> Système : Remplir le formulaire
    Administrateur -> Système : Cliquer sur "Enregistrer"
    Système -> Système : Valider les données
    Système -> Système : Créer un nouvel utilisateur
    Système -> Système : Enregistrer dans la base de données
    Système --> Administrateur : <PERSON>fficher "Utilisateur ajouté avec succès"
    Système --> Administrateur : Mettre à jour la liste des utilisateurs
end

@enduml
