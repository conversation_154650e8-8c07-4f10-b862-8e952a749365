@startuml Diagramme de Séquence - Gestion des Utilisateurs (MVC)

actor Administrateur
participant "<<View>>\nUsersManagementPage" as View
participant "<<Controller>>\nAdminController" as Controller
participant "<<Model>>\nUtilisateur" as Model
participant "<<Model>>\nEmailService" as EmailService

title Diagramme de Séquence - Gestion des Utilisateurs (Architecture MVC)

ref over Administrateur, EmailService : Gérer les utilisateurs

== Afficher les utilisateurs ==

Administrateur -> View : Cliquer sur "Gestion des utilisateurs"
activate View

View -> Controller : getApprovedUsers()
activate Controller

Controller -> Model : findApprovedUsers()
activate Model
Model --> Controller : users
deactivate Model

Controller --> View : users
deactivate Controller

View --> Administrateur : afficher liste des utilisateurs

== Ajouter un utilisateur ==

Administrateur -> View : Cliquer sur "Ajouter un utilisateur"
View --> Administrateur : afficher formulaire d'ajout

Administrateur -> View : Remplir le formulaire et soumettre
activate View

View -> Controller : addUser(userData)
activate Controller

Controller -> Model : create(userData)
activate Model
Model --> Controller : user
deactivate Model

Controller -> Controller : validate(user)
Controller -> Model : save(user)
activate Model
Model --> Controller : user
deactivate Model

Controller --> View : result
deactivate Controller

alt errors.length==0
    View --> Administrateur : afficher "Utilisateur ajouté avec succès"
else
    View --> Administrateur : afficher les erreurs
end

deactivate View

== Approuver un utilisateur ==

Administrateur -> View : Cliquer sur "Approuver"
View --> Administrateur : afficher boîte de dialogue de confirmation
Administrateur -> View : Confirmer l'approbation
activate View

View -> Controller : approveUser(userId)
activate Controller

Controller -> Model : find(userId)
activate Model
Model --> Controller : user
deactivate Model

Controller -> Model : setApproved(true)
activate Model
Model --> Controller : user
deactivate Model

Controller -> Model : save(user)
activate Model
Model --> Controller : user
deactivate Model

Controller -> EmailService : sendApprovalEmail(user)
activate EmailService
EmailService --> Controller : emailSent
deactivate EmailService

Controller --> View : result
deactivate Controller

alt errors.length==0
    View --> Administrateur : afficher "Utilisateur approuvé avec succès"
else
    View --> Administrateur : afficher les erreurs
end

deactivate View

== Rejeter un utilisateur ==

Administrateur -> View : Cliquer sur "Rejeter"
View --> Administrateur : afficher boîte de dialogue avec champ de raison
Administrateur -> View : Saisir la raison et confirmer
activate View

View -> Controller : rejectUser(userId, reason)
activate Controller

Controller -> Model : find(userId)
activate Model
Model --> Controller : user
deactivate Model

Controller -> EmailService : sendRejectionEmail(user, reason)
activate EmailService
EmailService --> Controller : emailSent
deactivate EmailService

Controller -> Model : delete(user)
activate Model
Model --> Controller : deleted
deactivate Model

Controller --> View : result
deactivate Controller

alt errors.length==0
    View --> Administrateur : afficher "Utilisateur rejeté avec succès"
else
    View --> Administrateur : afficher les erreurs
end

deactivate View

@enduml
