@startuml Authentification
actor Utilisateur
participant "Système" as Systeme

ref over Utilisateur, Systeme : S'authentifier sur la plateforme

== Inscription ==

Utilisateur -> Systeme : Accéder à la page d'inscription
Systeme --> Utilisateur : Afficher le formulaire d'inscription

Utilisateur -> Systeme : Remplir le formulaire (nom, email, mot de passe, téléphone, rôle)
Utilisateur -> Systeme : Télécharger une image de profil (optionnel)
Utilisateur -> Systeme : Cliquer sur "S'inscrire"

Systeme -> Systeme : Valider les données du formulaire

alt Données invalides
    Systeme --> Utilisateur : Afficher les erreurs de validation
else Données valides
    Systeme -> Systeme : Créer un compte utilisateur (statut: en attente d'approbation)
    Systeme -> Systeme : Enregistrer les informations dans la base de données
    Systeme --> Utilisateur : Afficher "Inscription réussie. Votre compte est en attente d'approbation par un administrateur."
end

== Connexion ==

Utilisateur -> Systeme : Accéder à la page de connexion
Systeme --> Utilisateur : Afficher le formulaire de connexion

Utilisateur -> Systeme : Saisir email et mot de passe
Utilisateur -> Systeme : Cliquer sur "Se connecter"

Systeme -> Systeme : Vérifier les identifiants

alt Identifiants invalides
    Systeme --> Utilisateur : Afficher "Identifiants invalides"
else Identifiants valides
    alt Compte non approuvé
        Systeme --> Utilisateur : Afficher "Votre compte est en attente d'approbation"
    else Compte approuvé
        Systeme -> Systeme : Générer un token JWT
        Systeme -> Systeme : Enregistrer la session utilisateur
        Systeme --> Utilisateur : Rediriger vers le tableau de bord correspondant au rôle
    end
end

== Déconnexion ==

Utilisateur -> Systeme : Cliquer sur "Déconnexion"
Systeme -> Systeme : Supprimer le token JWT
Systeme -> Systeme : Terminer la session utilisateur
Systeme --> Utilisateur : Rediriger vers la page d'accueil

== Réinitialisation Mot de Passe ==

Utilisateur -> Systeme : Accéder à la page "Mot de passe oublié"
Systeme --> Utilisateur : Afficher le formulaire de demande de réinitialisation

Utilisateur -> Systeme : Saisir l'email
Utilisateur -> Systeme : Cliquer sur "Réinitialiser mon mot de passe"

Systeme -> Systeme : Vérifier l'existence de l'email

alt Email existe
    Systeme -> Systeme : Générer un token de réinitialisation
    Systeme -> Systeme : Enregistrer le token dans la base de données
    Systeme -> Systeme : Envoyer un email avec le lien de réinitialisation
end

Systeme --> Utilisateur : Afficher "Si votre email est associé à un compte, vous recevrez un lien de réinitialisation"

... Utilisateur reçoit l'email et clique sur le lien ...

Utilisateur -> Systeme : Accéder à la page de réinitialisation avec le token
Systeme -> Systeme : Vérifier la validité du token

alt Token invalide ou expiré
    Systeme --> Utilisateur : Afficher "Token invalide ou expiré"
else Token valide
    Systeme --> Utilisateur : Afficher le formulaire de nouveau mot de passe
    
    Utilisateur -> Systeme : Saisir le nouveau mot de passe
    Utilisateur -> Systeme : Cliquer sur "Enregistrer"
    
    Systeme -> Systeme : Mettre à jour le mot de passe dans la base de données
    Systeme -> Systeme : Supprimer le token de réinitialisation
    
    Systeme --> Utilisateur : Afficher "Mot de passe réinitialisé avec succès"
    Systeme --> Utilisateur : Rediriger vers la page de connexion
end

@enduml
