@startuml Sous-Competence Add System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Ajout d'une sous-compétence"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Sélectionner un cours
System --> Admin : Afficher les détails du cours

Admin -> System : Sélectionner un quiz
System --> Admin : Afficher les détails du quiz avec ses compétences

Admin -> System : Sélectionner une compétence
System --> Admin : Afficher les détails de la compétence avec ses sous-compétences

Admin -> System : Cliquer sur "Ajouter une sous-compétence"
System --> Admin : Afficher le formulaire d'ajout de sous-compétence

Admin -> System : Remplir le formulaire (nom_fr, nom_en)
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Enregistrer la sous-compétence dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des sous-compétences avec la nouvelle sous-compétence
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

@enduml
