<?php
/**
 * Script pour démarrer le serveur Symfony avec HTTPS
 */

echo "=== Démarrage du serveur Symfony avec HTTPS ===\n\n";

// Vérifier si les certificats SSL existent
$sslDir = __DIR__ . '/config/ssl';
$keyFile = $sslDir . '/private.key';
$certFile = $sslDir . '/certificate.crt';

if (!file_exists($keyFile) || !file_exists($certFile)) {
    echo "⚠️  Certificats SSL non trouvés. Génération automatique...\n";
    
    // Exécuter le script de génération des certificats
    $output = [];
    $returnCode = 0;
    exec('php ' . __DIR__ . '/generate_ssl_certificates.php', $output, $returnCode);
    
    if ($returnCode !== 0) {
        echo "❌ Erreur lors de la génération des certificats SSL\n";
        exit(1);
    }
    
    echo "✅ Certificats SSL générés avec succès\n\n";
}

// Vérifier si le port 8001 est disponible
$socket = @fsockopen('127.0.0.1', 8001, $errno, $errstr, 1);
if ($socket) {
    fclose($socket);
    echo "⚠️  Le port 8001 est déjà utilisé. Arrêt du processus existant...\n";
    
    // Essayer d'arrêter le processus existant
    if (PHP_OS_FAMILY === 'Windows') {
        exec('netstat -ano | findstr :8001', $output);
        if (!empty($output)) {
            foreach ($output as $line) {
                if (preg_match('/\s+(\d+)$/', $line, $matches)) {
                    $pid = $matches[1];
                    exec("taskkill /F /PID $pid");
                    echo "✅ Processus $pid arrêté\n";
                }
            }
        }
    } else {
        exec('lsof -ti:8001 | xargs kill -9 2>/dev/null');
    }
    
    sleep(2); // Attendre que le port se libère
}

// Commandes pour démarrer le serveur
$commands = [
    // Nettoyer le cache
    'php bin/console cache:clear --env=dev',
    
    // Vérifier la configuration
    'php bin/console debug:config nelmio_cors',
    
    // Démarrer le serveur avec HTTPS
    sprintf(
        'php -S 127.0.0.1:8001 -t public/ router.php',
        $certFile,
        $keyFile
    )
];

echo "🚀 Démarrage du serveur Symfony...\n";
echo "📍 URL: https://127.0.0.1:8001\n";
echo "🔒 SSL: Activé (certificats auto-signés)\n";
echo "🌐 CORS: Configuré pour https://localhost:5174\n\n";

// Exécuter les commandes de préparation
foreach (array_slice($commands, 0, -1) as $command) {
    echo "⚙️  Exécution: $command\n";
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    if ($returnCode !== 0) {
        echo "⚠️  Avertissement lors de l'exécution de: $command\n";
        foreach ($output as $line) {
            echo "   $line\n";
        }
    }
}

echo "\n📋 Instructions:\n";
echo "1. Le serveur va démarrer sur https://127.0.0.1:8001\n";
echo "2. Votre navigateur affichera un avertissement de sécurité (certificat auto-signé)\n";
echo "3. Cliquez sur 'Avancé' puis 'Continuer vers le site' pour accepter le certificat\n";
echo "4. Pour arrêter le serveur, utilisez Ctrl+C\n\n";

echo "🔄 Démarrage du serveur...\n";

// Démarrer le serveur (dernière commande)
$serverCommand = end($commands);
echo "⚙️  Exécution: $serverCommand\n\n";

// Note: Pour un serveur HTTPS complet avec PHP built-in server,
// nous devrons utiliser un proxy ou configurer un serveur web réel
echo "⚠️  IMPORTANT: Le serveur PHP built-in ne supporte pas nativement HTTPS.\n";
echo "Pour un support HTTPS complet, considérez utiliser:\n";
echo "- Apache avec mod_ssl\n";
echo "- Nginx avec SSL\n";
echo "- Caddy (configuration automatique HTTPS)\n";
echo "- Docker avec un proxy SSL\n\n";

echo "🔄 Démarrage du serveur HTTP sur le port 8001...\n";
echo "📝 Note: Configurez votre serveur web pour rediriger HTTP vers HTTPS\n\n";

// Démarrer le serveur HTTP pour le moment
passthru($serverCommand);
