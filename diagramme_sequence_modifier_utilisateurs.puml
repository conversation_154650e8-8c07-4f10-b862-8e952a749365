@startuml
title Diagramme de séquence système - Modifier des utilisateurs

actor Administrateur
participant Système

== Modifier des utilisateurs ==

ref over Administrate<PERSON>, Système
    Modifier des utilisateurs
end ref

Administrateur -> Système : C<PERSON>r sur l'icône "Modifier" d'un utilisateur
Système -> Système : Récupérer les données de l'utilisateur
Système --> Administrateur : Afficher le formulaire de modification
Administrateur -> Système : Modifier les informations

alt [Assigner un rôle]
    Administrateur -> Système : Cliquer sur le menu déroulant de rôle d'un utilisateur
    Système --> Administrateur : Afficher les options de rôle
    Administrateur -> Système : Sélectionner un nouveau rôle
end

Administrateur -> Système : Cliquer sur "Enregistrer"
Système -> Système : Valider les données
Système -> Système : Mettre à jour l'utilisateur dans la base de données
Système --> Administrateur : Afficher "Utilisateur modifié avec succès"
Système --> Administrateur : Mettre à jour la liste des utilisateurs

@enduml
