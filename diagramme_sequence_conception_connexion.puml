@startuml Diagramme de Séquence de Conception - Connexion

actor Utilisateur
participant "<<View>>\nLogin.jsx" as LoginView
participant "<<ViewModel>>\nAuthContext" as AuthContext
participant "<<Model>>\nAuthController" as AuthController
participant "<<Model>>\nUtilisateur" as UtilisateurModel

title Diagramme de Séquence de Conception - Connexion

ref over Utilisateur, UtilisateurModel : Se connecter à la plateforme

Utilisateur -> LoginView : Cliquer sur "Se connecter"
activate LoginView

LoginView -> LoginView : Valider les données du formulaire
LoginView -> AuthContext : login(email, password)
activate AuthContext

AuthContext -> AuthController : POST /api/login
activate AuthController

AuthController -> UtilisateurModel : findByEmail(email)
activate UtilisateurModel
UtilisateurModel --> AuthController : utilisateur
deactivate UtilisateurModel

AuthController -> AuthController : verifyPassword(password)
AuthController -> AuthController : generateJwtToken(utilisateur)
AuthController --> AuthContext : response
deactivate AuthController

alt errors.length==0
    AuthContext --> LoginView : {success: true, user: data, token: token}
    LoginView --> Utilisateur : rediriger vers le tableau de bord
else
    alt Compte non approuvé
        AuthContext --> LoginView : {success: false, status: "pending"}
        LoginView --> Utilisateur : afficher "Votre compte est en attente d'approbation"
    else Identifiants invalides
        AuthContext --> LoginView : {success: false, error: message}
        LoginView --> Utilisateur : afficher "Identifiants invalides"
    end
end

deactivate AuthContext
deactivate LoginView

@enduml
