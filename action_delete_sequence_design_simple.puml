@startuml Action Delete Sequence Design

title Diagramme de Séquence - Suppression d'une action

actor Administrateur
participant "«View»\nActionForm.jsx" as View
participant "«Controller»\nActionController" as Controller
participant "«Model»\nAction" as Model

Administrateur -> View : Cliquer sur "Supprimer" pour une action
View --> Administrateur : Demander confirmation

Administrateur -> View : Confirmer la suppression
View -> Controller : DELETE /api/action/{id}
Controller -> Model : Supprimer l'action
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
