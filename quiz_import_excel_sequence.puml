@startuml Quiz Import Excel System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Import d'un quiz via Excel"

Admin -> System : Accéder à la page du formulaire de quiz
System --> Admin : Afficher la page correspondante

Admin -> System : Cliquer sur "Importer un quiz via Excel"
System --> Admin : Afficher le formulaire d'import Excel

Admin -> System : Sélectionner le fichier Excel
Admin -> System : Cliquer sur "Importer"
System -> System : Analyser le fichier Excel

alt [Fichier valide]
    System -> System : Extraire les données du quiz (informations de base)

    opt [Gestion des compétences]
        Admin -> System : Ajouter un bloc de compétence
        Admin -> System : Supprimer un bloc de compétence
        System -> System : Extraire les compétences
    end

    opt [Gestion des sous-compétences]
        Admin -> System : Ajouter un bloc de sous-compétence
        Admin -> System : Supprimer un bloc de sous-compétence
        System -> System : Extraire les sous-compétences
    end

    opt [Gestion des actions]
        Admin -> System : Ajouter un bloc d'action
        Admin -> System : Supprimer un bloc d'action
        System -> System : Extraire les actions
    end

    System -> System : Enregistrer toutes les données extraites
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des quiz du cours avec le quiz importé
else [Fichier invalide ou erreur d'importation]
    System --> Admin : Afficher les erreurs d'importation
end

@enduml
