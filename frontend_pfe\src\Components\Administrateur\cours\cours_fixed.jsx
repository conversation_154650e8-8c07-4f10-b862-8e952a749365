import { useState, useEffect } from "react";
import {
  FiSave,
  FiX,
  FiEdit2,
  FiTrash2,
  FiPlus,
  FiChevronDown,
  FiChevronUp,
  FiChevronRight,
  FiBook,
  FiFileText,
  FiHelpCircle,
} from "react-icons/fi";
import { useAuth } from "../../../contexts/auth-context";
import DialogModal from "../../Common/DialogModal";
import { API_URL } from "../../../config";

function CourseManagementPage() {
  // Utiliser l'URL de l'API depuis le fichier de configuration
  const API_BASE_URL = API_URL;
  const { token } = useAuth();
  const [showAddCourseForm, setShowAddCourseForm] = useState(false);
  const [courses, setCourses] = useState([]);
  const [error, setError] = useState(null);
  const [expandedCourses, setExpandedCourses] = useState([]);
  const [expandedQuizzes, setExpandedQuizzes] = useState([]);
  const [expandedCompetences, setExpandedCompetences] = useState([]);
  const [loadingCourses, setLoadingCourses] = useState({});
  const [editingCourse, setEditingCourse] = useState(null);
  const [editingQuiz, setEditingQuiz] = useState(null);
  const [editingCompetence, setEditingCompetence] = useState(null);
  const [editingSousCompetence, setEditingSousCompetence] = useState(null);
  const [editingAction, setEditingAction] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // État pour les boîtes de dialogue
  const [dialog, setDialog] = useState({
    show: false,
    title: "",
    message: "",
    type: "info", // 'info', 'success', 'error', 'confirm'
    onConfirm: null,
    confirmText: "OK",
    cancelText: "Annuler",
  });

  // Fonction pour récupérer tous les cours
  const fetchCourses = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/cours`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const coursesData = data["hydra:member"] || data;

      const coursesWithQuizzes = await Promise.all(
        coursesData.map(async (course) => {
          try {
            const quizzes = await fetchQuizzesForCourse(course.id);
            return { ...course, quizzes };
          } catch (error) {
            return { ...course, quizzes: [] };
          }
        })
      );

      return coursesWithQuizzes;
    } catch (err) {
      setError(err.message);
      return [];
    }
  };

  useEffect(() => {
    // Charger les cours au chargement du composant
    const loadCourses = async () => {
      setIsLoading(true);
      try {
        const coursesData = await fetchCourses();
        setCourses(coursesData);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    loadCourses();
  }, [token]);

  const fetchQuizzesForCourse = async (courseId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/quiz?cours=${courseId}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const quizzesData = data["hydra:member"] || data;

      // Traiter les quiz avec leurs compétences et sous-compétences
      const processedQuizzes = quizzesData.map((quiz) => {
        // S'assurer que les noms sont des chaînes complètes
        const processedQuiz = {
          ...quiz,
          id: quiz.id || `quiz-${Math.random().toString(36).substr(2, 9)}`,
          Nom_FR: quiz.Nom_FR || "",
          Nom_EN: quiz.Nom_EN || "",
        };

        // Traiter les compétences si elles existent
        if (quiz.competences && Array.isArray(quiz.competences)) {
          processedQuiz.competences = quiz.competences.map((competence) => {
            // Traiter chaque compétence
            const processedCompetence = {
              ...competence,
              id: competence.id,
              Competence_ID: competence.id,
              Competence_Nom_FR: competence.nom_fr || "",
              Competence_Nom_EN: competence.nom_en || "",
              Comp_Categorie_FR: competence.categorie_fr || "",
              Comp_Categorie_EN: competence.categorie_en || "",
            };

            // Traiter les sous-compétences si elles existent
            if (
              competence.sousCompetences &&
              Array.isArray(competence.sousCompetences)
            ) {
              processedCompetence.sousCompetences =
                competence.sousCompetences.map((sousComp) => ({
                  id:
                    sousComp.id ||
                    `sub-${Math.random().toString(36).substr(2, 9)}`,
                  SousCompetence_Nom_FR: sousComp.nom_fr || "",
                  SousCompetence_Nom_EN: sousComp.nom_en || "",
                }));
            } else {
              processedCompetence.sousCompetences = [];
            }

            return processedCompetence;
          });
        } else {
          processedQuiz.competences = [];
        }

        // Traiter les actions si elles existent
        if (quiz.actions && Array.isArray(quiz.actions)) {
          processedQuiz.actions = quiz.actions.map((action) => ({
            id: action.id || `act-${Math.random().toString(36).substr(2, 9)}`,
            Action_Nom_FR: action.nom_fr || "",
            Action_Nom_EN: action.nom_en || "",
            Action_Categorie_FR: action.categorie_fr || "",
            Action_Categorie_EN: action.categorie_en || "",
          }));
        } else {
          processedQuiz.actions = [];
        }

        return processedQuiz;
      });

      // Regrouper les quiz par IDModule pour éviter les doublons
      const quizzesByIDModule = {};
      processedQuizzes.forEach((quiz) => {
        if (!quizzesByIDModule[quiz.IDModule]) {
          quizzesByIDModule[quiz.IDModule] = quiz;
        }
      });

      return Object.values(quizzesByIDModule);
    } catch (error) {
      console.error("Error fetching quizzes:", error);
      return [];
    }
  };

  const createCourse = async (courseData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/cours`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(courseData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || errorData.title || "Failed to create course"
        );
      }

      return await response.json();
    } catch (err) {
      throw err;
    }
  };

  const updateCourse = async (id, courseData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/cours/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(courseData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update course");
      }

      return await response.json();
    } catch (err) {
      throw err;
    }
  };

  const deleteCourse = async (id) => {
    try {
      const response = await fetch(`${API_BASE_URL}/cours/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete course");
      }

      return true;
    } catch (err) {
      throw err;
    }
  };

  const updateQuiz = async (id, quizData) => {
    try {
      // Trouver le quiz dans les cours pour obtenir son IDModule
      let quizIDModule = null;
      let quizToUpdate = null;

      for (const course of courses) {
        for (const quiz of course.quizzes || []) {
          if (quiz.id === id) {
            quizIDModule = quiz.IDModule;
            quizToUpdate = quiz;
            break;
          }
        }
        if (quizIDModule) break;
      }

      if (!quizIDModule) {
        throw new Error(`Quiz avec ID ${id} non trouvé`);
      }

      // Utiliser l'IDModule pour la mise à jour
      // Note: Le backend ne met à jour que MainSurface, pas Main et Surface
      const response = await fetch(`${API_BASE_URL}/quiz/${quizIDModule}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...quizData,
          // Inclure Main et Surface au cas où le backend serait mis à jour à l'avenir
          Main: quizData.Main,
          Surface: quizData.Surface,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update quiz");
      }

      const result = await response.json();

      return result;
    } catch (err) {
      throw err;
    }
  };

  // Fonction pour mettre à jour un quiz par son IDModule
  const updateQuizByIDModule = async (idModule, quizData) => {
    try {
      // Vérifier si l'IDModule est valide
      if (!idModule) {
        throw new Error("IDModule est requis pour mettre à jour un quiz");
      }

      // Construire l'URL avec l'IDModule
      const url = `${API_BASE_URL}/quiz/${idModule}`;

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(quizData),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          throw new Error(`Erreur HTTP: ${response.status}`);
        }
        throw new Error(
          errorData.message ||
            `Échec de la mise à jour du quiz: ${response.status}`
        );
      }

      const result = await response.json();
      return result;
    } catch (err) {
      throw err;
    }
  };

  const deleteQuiz = async (quizIDModule) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/quiz/by-idmodule/${quizIDModule}`,
        {
          method: "DELETE",
          headers: {
            Accept: "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        let errorData = {};
        try {
          errorData = await response.json();
        } catch (e) {}
        if (response.status === 404) {
          return {
            success: false,
            message: "Quiz not found or already deleted",
          };
        }
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }
      return { success: true };
    } catch (err) {
      throw err;
    }
  };

  const handleAddCourse = async (newCourse) => {
    try {
      const createdCourse = await createCourse({
        titre: newCourse.title,
        description: newCourse.description,
      });
      setCourses([...courses, createdCourse]);
      setShowAddCourseForm(false);

      // Afficher une boîte de dialogue de succès
      setDialog({
        show: true,
        title: "Succès",
        message: "Cours ajouté avec succès",
        type: "success",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    } catch (err) {
      setError(err.message);

      // Afficher une boîte de dialogue d'erreur
      setDialog({
        show: true,
        title: "Erreur",
        message: `Échec de l'ajout du cours: ${err.message}`,
        type: "error",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    }
  };

  const handleUpdateCourse = async (updatedCourse) => {
    try {
      const updated = await updateCourse(updatedCourse.id, {
        titre: updatedCourse.title,
        description: updatedCourse.description,
      });
      setCourses(
        courses.map((course) => (course.id === updated.id ? updated : course))
      );
      setEditingCourse(null);

      // Afficher une boîte de dialogue de succès
      setDialog({
        show: true,
        title: "Succès",
        message: "Cours mis à jour avec succès",
        type: "success",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    } catch (err) {
      setError(err.message);

      // Afficher une boîte de dialogue d'erreur
      setDialog({
        show: true,
        title: "Erreur",
        message: `Échec de la mise à jour du cours: ${err.message}`,
        type: "error",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    }
  };

  const handleDeleteCourse = async (courseId) => {
    try {
      await deleteCourse(courseId);
      setCourses(courses.filter((course) => course.id !== courseId));

      // Afficher une boîte de dialogue de succès
      setDialog({
        show: true,
        title: "Succès",
        message: "Cours supprimé avec succès",
        type: "success",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    } catch (err) {
      setError(err.message);

      // Afficher une boîte de dialogue d'erreur
      setDialog({
        show: true,
        title: "Erreur",
        message: `Échec de la suppression du cours: ${err.message}`,
        type: "error",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    }
  };

  const handleAddQuizAction = async (courseId, quizId) => {
    console.log("Ajout d'une action pour le quiz:", quizId);
    try {
      // Trouver le quiz pour obtenir son ID réel
      let quiz = null;

      for (const course of courses) {
        for (const q of course.quizzes || []) {
          if (q.IDModule === quizId) {
            quiz = q;
            break;
          }
        }
        if (quiz) break;
      }

      if (!quiz) {
        throw new Error(`Quiz avec ID ${quizId} non trouvé`);
      }

      // Créer une nouvelle action avec des valeurs par défaut
      const newAction = {
        id: Date.now(), // ID temporaire pour l'interface
        Action_Nom_FR: "Nouvelle Action",
        Action_Nom_EN: "New Action",
        Action_Categorie_FR: "",
        Action_Categorie_EN: "",
      };

      // Mettre à jour l'état local d'abord pour une meilleure expérience utilisateur
      setCourses(
        courses.map((course) => ({
          ...course,
          quizzes:
            course.quizzes?.map((q) =>
              q.IDModule === quizId
                ? {
                    ...q,
                    actions: [...(q.actions || []), newAction],
                  }
                : q
            ) || [],
        }))
      );

      // Appeler l'API pour créer l'action en utilisant la route correcte
      console.log("Données envoyées pour createAction:", {
        IDModule: quizId,
        Action_Nom_FR: newAction.Action_Nom_FR,
        Action_Nom_EN: newAction.Action_Nom_EN,
        Action_Categorie_FR: newAction.Action_Categorie_FR || "",
        Action_Categorie_EN: newAction.Action_Categorie_EN || "",
      });

      const response = await fetch(`${API_BASE_URL}/quiz/action/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          IDModule: quizId, // Utiliser IDModule comme attendu par QuizController
          Action_Nom_FR: newAction.Action_Nom_FR,
          Action_Nom_EN: newAction.Action_Nom_EN,
          Action_Categorie_FR: newAction.Action_Categorie_FR || "",
          Action_Categorie_EN: newAction.Action_Categorie_EN || "",
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Erreur API:", response.status, errorText);
        throw new Error(`Erreur ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log("Action créée avec succès:", responseData);

      // Recharger les données complètes pour s'assurer que nous avons les dernières informations
      const refreshedCourses = await fetchCourses();
      setCourses(refreshedCourses);

      setDialog({
        show: true,
        title: "Succès",
        message: "Action ajoutée avec succès",
        type: "success",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    } catch (err) {
      console.error("Erreur lors de l'ajout de l'action:", err);
      setDialog({
        show: true,
        title: "Erreur",
        message: `Échec de l'ajout: ${err.message}`,
        type: "error",
        onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
        confirmText: "OK",
      });
    }
  };

  // Reste du code...
}

export default CourseManagementPage;
