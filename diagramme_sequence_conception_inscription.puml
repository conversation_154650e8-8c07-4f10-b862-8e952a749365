@startuml Diagramme de Séquence de Conception - Inscription

actor Utilisateur
participant "<<View>>\nRegister.jsx" as RegisterView
participant "<<ViewModel>>\nAuthContext" as AuthContext
participant "<<Model>>\nAuthController" as AuthController
participant "<<Model>>\nUtilisateur" as UtilisateurModel

title Diagramme de Séquence de Conception - Inscription

ref over Utilisateur, UtilisateurModel : S'inscrire sur la plateforme

Utilisateur -> RegisterView : Cliquer sur "S'inscrire"
activate RegisterView

RegisterView -> RegisterView : Valider les données du formulaire
RegisterView -> AuthContext : register(userData)
activate AuthContext

AuthContext -> AuthController : POST /api/register
activate AuthController

AuthController -> UtilisateurModel : create(userData)
activate UtilisateurModel
UtilisateurModel --> AuthController : utilisateur
deactivate UtilisateurModel

AuthController -> AuthController : validate(utilisateur)
AuthController -> AuthController : persist(utilisateur)
AuthController --> AuthContext : response
deactivate AuthController

alt errors.length==0
    AuthContext --> RegisterView : {success: true, user: data}
    RegisterView --> Utilisateur : afficher "Inscription réussie. Votre compte est en attente d'approbation."
else
    AuthContext --> RegisterView : {success: false, error: messages}
    RegisterView --> Utilisateur : afficher les erreurs
end

deactivate AuthContext
deactivate RegisterView

@enduml
