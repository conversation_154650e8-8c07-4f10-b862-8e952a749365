@startuml Course Add System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Ajout d'un cours"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Cliquer sur "Ajouter un cours"
System --> Admin : Afficher le formulaire d'ajout de cours

Admin -> System : Remplir le formulaire (titre, description)
Admin -> System : Cliquer sur "Enregistrer"
System -> System : Valider les données du formulaire

alt [Données valides]
    System -> System : Enregistrer le cours dans la base de données
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des cours avec le nouveau cours
else [Données invalides]
    System --> Admin : Afficher les erreurs de validation
end

@enduml
