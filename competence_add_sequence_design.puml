@startuml Competence Add Sequence Design

title Diagramme de Séquence - Ajout d'une compétence

actor Utilisateur
participant "«View»\nCompetenceForm.jsx" as View
participant "«Controller»\nCompetenceContext" as Context
participant "«Controller»\nCompetenceController" as Controller
participant "«Model»\nCompetence" as Model

ref over Utilisateur, Model
  Ajouter une compétence
end ref

Utilisateur -> View : Cliquer sur "Ajouter une compétence"
View --> Utilisateur : Afficher le formulaire d'ajout de compétence

Utilisateur -> View : Remplir le formulaire (nom_fr, nom_en, categorie_fr, categorie_en)
Utilisateur -> View : Cliquer sur "Enregistrer"
View -> View : Valider les données du formulaire
View -> Context : addCompetence(competenceData)

Context -> Controller : POST /api/competence
Controller -> Model : Valider les données
Model -> Model : create(competenceData)
Model -> Model : persist(competence)
Model --> Controller : response

alt [success: true]
    Controller --> Context : {success: true, competence: data}
    Context --> View : Mettre à jour la liste des compétences
    View --> Utilisateur : Afficher "Compétence ajoutée avec succès"
else [success: false]
    Controller --> Context : {success: false, error: messages}
    Context --> View : Transmettre les erreurs
    View --> Utilisateur : Afficher les erreurs
end

@enduml
