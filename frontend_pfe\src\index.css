@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html,
body,
#root {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  @apply min-h-screen flex flex-col bg-gray-50 dark:bg-gray-900;
}
@layer base {
  body {
    @apply [scrollbar-color:_#cbd5e1_transparent] dark:[scrollbar-color:_#334155_transparent];
    overflow-x: hidden;
  }

  html,
  body {
    overflow-x: hidden;
    width: 100%;
  }

  /* Personnalisation de la barre de défilement */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(148, 163, 184, 0.3);
    border-radius: 20px;
    border: 2px solid transparent;
    background-clip: padding-box;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(148, 163, 184, 0.5);
  }

  /* Version sombre */
  .dark ::-webkit-scrollbar-thumb {
    background-color: rgba(51, 65, 85, 0.3);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(51, 65, 85, 0.5);
  }
}

@layer components {
  .sidebar-group {
    @apply flex w-full flex-col gap-y-2;
  }

  .sidebar-group-title {
    @apply overflow-hidden text-ellipsis text-sm font-medium text-slate-600 dark:text-slate-400;
  }

  .sidebar-item {
    @apply flex h-[40px] w-full flex-shrink-0 items-center gap-x-3 rounded-lg p-3 text-base font-medium text-slate-900 transition-colors hover:bg-blue-50 dark:text-slate-50 dark:hover:bg-blue-950;
    &.active {
      @apply bg-blue-500 text-slate-50 dark:bg-blue-600;
    }
  }

  .title {
    @apply text-3xl font-semibold text-slate-900 transition-colors dark:text-slate-50;
  }

  .btn-ghost {
    @apply flex h-10 flex-shrink-0 items-center justify-center gap-x-2 rounded-lg p-2 text-slate-400 transition-colors hover:bg-blue-50 hover:text-slate-500 dark:hover:bg-blue-950 dark:hover:text-slate-300;
  }

  .link {
    @apply text-base font-medium text-slate-900 transition-colors hover:underline dark:text-slate-50;
  }

  .input {
    @apply hidden h-10 flex-shrink-0 items-center gap-x-2 rounded-lg border border-slate-300 px-2 text-base text-slate-900 transition-colors has-[input:focus]:border-blue-500 md:flex md:w-auto lg:w-80 dark:border-slate-700 dark:text-slate-50 dark:focus:border-blue-600;
  }

  .card {
    @apply flex flex-col gap-y-4 rounded-lg border border-slate-300 bg-white p-4 transition-colors dark:border-slate-700 dark:bg-slate-900;
  }

  .card-header {
    @apply flex items-center gap-x-2;
  }

  .card-title {
    @apply font-medium text-slate-900 transition-colors dark:text-slate-50;
  }

  .card-body {
    @apply flex flex-col gap-y-2 rounded-lg p-4;
  }

  .table {
    @apply h-full w-full text-slate-900 dark:text-slate-50;
  }

  .table-header {
    @apply sticky top-0 bg-slate-200 transition-[background] dark:bg-slate-800;
  }

  .table-row {
    @apply border-b border-slate-300 transition-colors last:border-none dark:border-slate-700;
  }

  .table-head {
    @apply h-12 px-4 text-start;
  }

  .table-cell {
    @apply w-fit whitespace-nowrap p-4 font-medium;
  }
}

.recharts-default-tooltip {
  @apply !rounded-lg !border !border-slate-300 !bg-white transition-colors dark:!border-slate-700 dark:!bg-slate-900;
}

.recharts-tooltip-label {
  @apply text-base font-medium text-slate-900 dark:text-slate-50;
}

.recharts-tooltip-item {
  @apply text-base font-medium !text-blue-500 dark:!text-blue-600;
}
.title {
  @apply text-3xl font-semibold text-slate-900 transition-colors dark:text-slate-50;
}
.btn-ghost {
  @apply flex h-10 flex-shrink-0 items-center justify-center gap-x-2 rounded-lg p-2 text-slate-400 transition-colors hover:bg-blue-50 hover:text-slate-500 dark:hover:bg-blue-950 dark:hover:text-slate-300;
}
.input {
  @apply hidden h-10 flex-shrink-0 items-center gap-x-2 rounded-lg border border-slate-300 px-2 text-base text-slate-900 transition-colors has-[input:focus]:border-blue-500 md:flex md:w-auto lg:w-80 dark:border-slate-700 dark:text-slate-50 dark:focus:border-blue-600;
}

.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors;
}
.btn-secondary {
  @apply bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 px-3 py-2 rounded-lg transition-colors dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600;
}
.btn-outline {
  @apply bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 px-3 py-1 rounded-md transition-colors dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-600;
}
.btn-outline-red {
  @apply bg-white hover:bg-gray-50 text-red-600 border border-red-300 px-3 py-1 rounded-md transition-colors dark:bg-gray-700 dark:border-red-600 dark:text-red-400 dark:hover:bg-gray-600;
}

/* Animations pour l'interface de réclamation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out;
}

/* Notification styles */
.notification-container {
  @apply fixed top-4 right-4 z-50 max-w-sm;
}

.notification-dropdown {
  @apply absolute right-0 mt-2 w-80 rounded-lg shadow-xl bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 z-50 overflow-hidden;
  animation: slideInDown 0.3s ease-out;
  transform-origin: top right;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.notification-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200 dark:border-slate-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-blue-900/20;
}

.notification-title {
  @apply font-semibold text-gray-800 dark:text-white flex items-center gap-2;
}

.notification-actions {
  @apply flex items-center gap-2;
}

.notification-action-btn {
  @apply text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors;
}

.notification-list {
  @apply max-h-[70vh] overflow-y-auto;
}

.notification-item {
  @apply p-4 border-b border-gray-100 dark:border-slate-700 cursor-pointer transition-all hover:shadow-inner;
}

.notification-item:hover {
  @apply bg-gray-50 dark:bg-slate-700/50 transform scale-[0.99] transition-transform;
}

.notification-item.unread {
  @apply bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 border-l-4 border-l-blue-500;
  animation: fadeIn 0.5s ease-out;
}

.notification-content {
  @apply flex items-start gap-3 relative;
}

.notification-icon {
  @apply flex-shrink-0 p-2 rounded-full;
}

.notification-delete {
  @apply ml-auto flex-shrink-0 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity;
}

.notification-icon.message {
  @apply bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400;
}

.notification-icon.alert {
  @apply bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400;
}

.notification-icon.success {
  @apply bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400;
}

.notification-icon.info {
  @apply bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400;
}

.notification-text {
  @apply flex-1;
}

.notification-message {
  @apply text-sm text-gray-800 dark:text-gray-200;
}

.notification-time {
  @apply text-xs text-gray-500 dark:text-gray-400 mt-1;
}

.notification-badge {
  @apply absolute top-0 right-0 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center;
  box-shadow: 0 0 0 2px white;
  animation: pulse 2s infinite;
}

.dark .notification-badge {
  box-shadow: 0 0 0 2px #1e293b;
}

/* Pulse animation for notification badge */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.6);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
  }
}

@keyframes slideInDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.notification-badge-pulse {
  animation: pulse 2s infinite;
}

.notification-empty {
  @apply p-4 text-center text-gray-500 dark:text-gray-400;
}

.notification-filters {
  @apply flex items-center gap-2 px-3 py-2 border-b border-gray-200 dark:border-slate-700 bg-gray-50/50 dark:bg-slate-700/30 overflow-x-auto;
}

.filter-btn {
  @apply px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors;
}

.filter-btn.active {
  @apply bg-blue-500 text-white hover:bg-blue-600 dark:hover:bg-blue-600;
}

.notification-filter {
  @apply text-xs px-2 py-1 rounded-full bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 text-gray-600 dark:text-gray-300 cursor-pointer transition-colors;
}

.notification-filter:hover {
  @apply bg-gray-50 dark:bg-slate-700;
}

.notification-filter.active {
  @apply bg-blue-500 text-white border-blue-500 dark:bg-blue-600 dark:border-blue-600;
}

/* Background patterns */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Glass morphism effects */
.glass-morphism {
  @apply bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50;
}

.glass-card {
  @apply bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-gray-100 dark:border-gray-700 rounded-2xl shadow-card hover:shadow-card-hover transition-all duration-300;
}

/* Dashboard animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10%,
  90% {
    opacity: 0.5;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-shimmer {
  animation: shimmer 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(0.98);
  }
  50% {
    opacity: 0.8;
    transform: scale(1);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
}

.animate-breathe {
  animation: breathe 8s ease-in-out infinite;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-3000 {
  animation-delay: 3s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0);
  }
  25% {
    transform: translateY(-5px) rotate(-1deg);
  }
  50% {
    transform: translateY(-10px) rotate(0);
  }
  75% {
    transform: translateY(-5px) rotate(1deg);
  }
}

.animate-float {
  animation: float 10s ease-in-out infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient-shift 8s ease infinite;
  background-size: 200% 200%;
}

@keyframes glow {
  0%,
  100% {
    box-shadow:
      0 0 5px rgba(59, 130, 246, 0.3),
      0 0 20px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow:
      0 0 10px rgba(59, 130, 246, 0.5),
      0 0 30px rgba(59, 130, 246, 0.2);
  }
}

.animate-glow {
  animation: glow 4s ease-in-out infinite;
}

@keyframes slide-up-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up-fade-in 0.5s ease-out forwards;
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-5px);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s infinite;
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

/* Shadow effects */
.shadow-card {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03),
    0 1px 0 rgba(255, 255, 255, 0.1) inset;
}

.dark .shadow-card {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.05) inset;
}

.shadow-card-hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.08),
    0 8px 10px -6px rgba(0, 0, 0, 0.04),
    0 1px 0 rgba(255, 255, 255, 0.15) inset;
}

.dark .shadow-card-hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 8px 10px -6px rgba(0, 0, 0, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.05) inset;
}

.shadow-inner-light {
  box-shadow:
    inset 0 2px 4px 0 rgba(0, 0, 0, 0.03),
    inset 0 1px 2px 0 rgba(0, 0, 0, 0.02);
}

.dark .shadow-inner-light {
  box-shadow:
    inset 0 2px 4px 0 rgba(0, 0, 0, 0.2),
    inset 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}

.shadow-3d {
  box-shadow:
    0 1px 1px rgba(0, 0, 0, 0.02),
    0 2px 2px rgba(0, 0, 0, 0.02),
    0 4px 4px rgba(0, 0, 0, 0.02),
    0 8px 8px rgba(0, 0, 0, 0.02),
    0 16px 16px rgba(0, 0, 0, 0.02);
}

.dark .shadow-3d {
  box-shadow:
    0 1px 1px rgba(0, 0, 0, 0.1),
    0 2px 2px rgba(0, 0, 0, 0.1),
    0 4px 4px rgba(0, 0, 0, 0.1),
    0 8px 8px rgba(0, 0, 0, 0.1),
    0 16px 16px rgba(0, 0, 0, 0.1);
}

.shadow-glow {
  box-shadow:
    0 0 5px rgba(59, 130, 246, 0.2),
    0 0 20px rgba(59, 130, 246, 0.1);
}

.dark .shadow-glow {
  box-shadow:
    0 0 5px rgba(59, 130, 246, 0.3),
    0 0 20px rgba(59, 130, 246, 0.15);
}

/* Dashboard specific styles */
.dashboard-card {
  @apply bg-white/90 dark:bg-slate-800/80 backdrop-blur-md rounded-2xl border border-white/50 dark:border-gray-700/30 transition-all duration-300 overflow-hidden relative transform hover:-translate-y-1 hover:rotate-[0.5deg];
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03),
    0 1px 0 rgba(255, 255, 255, 0.1) inset;
}

.dashboard-card:hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.08),
    0 8px 10px -6px rgba(0, 0, 0, 0.04),
    0 1px 0 rgba(255, 255, 255, 0.15) inset,
    0 0 15px rgba(59, 130, 246, 0.1);
}

.dark .dashboard-card {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.05) inset;
}

.dark .dashboard-card:hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 8px 10px -6px rgba(0, 0, 0, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.05) inset,
    0 0 15px rgba(59, 130, 246, 0.15);
}

.dashboard-card-header {
  @apply flex items-center justify-between mb-6 relative z-10;
}

.dashboard-card-title {
  @apply text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.dark .dashboard-card-title {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.dashboard-card-content {
  @apply bg-gradient-to-br from-gray-50 to-white dark:from-gray-800/50 dark:to-gray-800/30 rounded-xl p-5;
  box-shadow:
    inset 0 2px 4px 0 rgba(0, 0, 0, 0.03),
    inset 0 1px 2px 0 rgba(0, 0, 0, 0.02);
}

.dark .dashboard-card-content {
  box-shadow:
    inset 0 2px 4px 0 rgba(0, 0, 0, 0.2),
    inset 0 1px 2px 0 rgba(0, 0, 0, 0.1);
}

.dashboard-progress-bar {
  @apply h-3 rounded-full transition-all duration-500 ease-out relative;
  background: linear-gradient(90deg, #60a5fa, #3b82f6, #2563eb);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

.dark .dashboard-progress-bar {
  background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

.dashboard-progress-bar-shine {
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer;
}

/* Neuomorphic elements */
.neuomorphic-button {
  @apply bg-gray-100 dark:bg-gray-800 rounded-xl transition-all duration-300;
  box-shadow:
    5px 5px 10px rgba(0, 0, 0, 0.03),
    -5px -5px 10px rgba(255, 255, 255, 0.7),
    inset 0 0 0 rgba(0, 0, 0, 0),
    inset 0 0 0 rgba(255, 255, 255, 0);
}

.neuomorphic-button:hover {
  box-shadow:
    0 0 0 rgba(0, 0, 0, 0),
    0 0 0 rgba(255, 255, 255, 0),
    inset 5px 5px 10px rgba(0, 0, 0, 0.03),
    inset -5px -5px 10px rgba(255, 255, 255, 0.7);
}

.dark .neuomorphic-button {
  box-shadow:
    5px 5px 10px rgba(0, 0, 0, 0.2),
    -5px -5px 10px rgba(255, 255, 255, 0.05),
    inset 0 0 0 rgba(0, 0, 0, 0),
    inset 0 0 0 rgba(255, 255, 255, 0);
}

.dark .neuomorphic-button:hover {
  box-shadow:
    0 0 0 rgba(0, 0, 0, 0),
    0 0 0 rgba(255, 255, 255, 0),
    inset 5px 5px 10px rgba(0, 0, 0, 0.2),
    inset -5px -5px 10px rgba(255, 255, 255, 0.05);
}

/* Glass card with 3D effect */
.glass-card-3d {
  @apply bg-white/80 dark:bg-slate-800/80 backdrop-blur-md rounded-2xl border border-white/50 dark:border-gray-700/30 transition-all duration-300 overflow-hidden relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.glass-card-3d:hover {
  transform: translateY(-5px) rotateX(2deg) rotateY(2deg);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.dark .glass-card-3d {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

.dark .glass-card-3d:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}
