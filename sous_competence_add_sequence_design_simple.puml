@startuml Sous-Competence Add Sequence Design

title Diagramme de Séquence - Ajout d'une sous-compétence

actor Administrateur
participant "«View»\nSousCompetenceForm.jsx" as View
participant "«Controller»\nSousCompetenceController" as Controller
participant "«Model»\nSousCompetence" as Model

Administrateur -> View : Remplir le formulaire (nom_fr, nom_en)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : POST /api/sous_competence
Controller -> Model : Enregistrer la sous-compétence
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
