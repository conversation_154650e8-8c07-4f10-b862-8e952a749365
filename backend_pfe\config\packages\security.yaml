security:
  # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
  password_hashers:
    Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: "auto"
  # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
  providers:
    # used to reload user from session & other features (e.g. switch_user)
    app_user_provider:
      entity:
        class: App\Entity\Utilisateur
        property: email
  firewalls:
    dev:
      pattern: ^/(_(profiler|wdt)|css|images|js)/
      security: false
    login:
      pattern: ^/api/login
      stateless: true
      json_login:
        check_path: /api/login
        username_path: email
        password_path: password
    register:
      pattern: ^/api/register
      security: false
    password_reset:
      pattern: ^/api/(forgot-password|reset-password)
      security: false
    # ollama_test a été supprimé
    api:
      pattern: ^/api
      stateless: true
      custom_authenticator: App\Security\JwtAuthenticator
      provider: app_user_provider
      entry_point: App\Security\JwtAuthenticator
    main:
      lazy: true
      provider: app_user_provider

  # Easy way to control access for large sections of your site
  # Note: Only the *first* access control that matches will be used
  access_control:
    - { path: ^/api/login, roles: PUBLIC_ACCESS }
    - { path: ^/api/register, roles: PUBLIC_ACCESS }
    - { path: ^/api/forgot-password, roles: PUBLIC_ACCESS }
    - { path: ^/api/reset-password, roles: PUBLIC_ACCESS }
    # Les routes du chatbot ont été supprimées
    - { path: ^/api/admin, roles: ROLE_ADMINISTRATEUR }
    - { path: ^/api/formateur, roles: ROLE_FORMATEUR }
    - { path: ^/api/apprenant, roles: ROLE_APPRENANT }
    - { path: ^/api/progression, roles: [ROLE_FORMATEUR, ROLE_APPRENANT] }
    - { path: ^/api/certificat, roles: [ROLE_FORMATEUR, ROLE_APPRENANT] }
    - { path: ^/api/evenement, roles: ROLE_ADMINISTRATEUR }
    - { path: ^/api, roles: IS_AUTHENTICATED_FULLY }

when@test:
  security:
    password_hashers:
      # By default, password hashers are resource intensive and take time. This is
      # important to generate secure password hashes. In tests however, secure hashes
      # are not important, waste resources and increase test times. The following
      # reduces the work factor to the lowest possible values.
      Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
        algorithm: auto
        cost: 4 # Lowest possible value for bcrypt
        time_cost: 3 # Lowest possible value for argon
        memory_cost: 10 # Lowest possible value for argon
