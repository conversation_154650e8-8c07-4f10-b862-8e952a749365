@startuml Diagramme de Séquence - Gestion des Utilisateurs (MVC)

actor Administrateur
participant "<<View>>\nUsersManagementPage" as View
participant "<<Controller>>\nAuthContext" as Controller
participant "<<Controller>>\nAdminController" as BackendController
participant "<<Model>>\nUtilisateur" as Model
participant "<<Service>>\nEmailService" as EmailService

title Diagramme de Séquence - Gestion des Utilisateurs (Architecture MVC)

ref over Administrateur, EmailService : Gérer les utilisateurs

== Afficher les utilisateurs ==

Administrateur -> View : Cliquer sur "Gestion des utilisateurs"
activate View

View -> Controller : getApprovedUsers()
activate Controller

Controller -> BackendController : GET /api/admin/users
activate BackendController

BackendController -> Model : findApprovedUsers()
activate Model
Model --> BackendController : users
deactivate Model

BackendController --> Controller : response
deactivate BackendController

Controller --> View : {success: true, users: data}
deactivate Controller

View --> Administrateur : afficher liste des utilisateurs

== Ajouter un utilisateur ==

Administrateur -> View : Cliquer sur "Ajouter un utilisateur"
View --> Administrateur : afficher formulaire d'ajout

Administrateur -> View : Remplir le formulaire et soumettre
activate View

View -> Controller : addUser(userData)
activate Controller

Controller -> BackendController : POST /api/admin/users
activate BackendController

BackendController -> Model : create(userData)
activate Model
Model --> BackendController : user
deactivate Model

BackendController -> BackendController : validate(user)
BackendController -> BackendController : persist(user)
BackendController --> Controller : response
deactivate BackendController

alt errors.length==0
    Controller --> View : {success: true, user: data}
    View --> Administrateur : afficher "Utilisateur ajouté avec succès"
else
    Controller --> View : {success: false, error: messages}
    View --> Administrateur : afficher les erreurs
end

deactivate Controller
deactivate View

== Approuver un utilisateur ==

Administrateur -> View : Cliquer sur "Approuver"
View --> Administrateur : afficher boîte de dialogue de confirmation
Administrateur -> View : Confirmer l'approbation
activate View

View -> Controller : approveUser(userId)
activate Controller

Controller -> BackendController : POST /api/admin/users/approve/{id}
activate BackendController

BackendController -> Model : find(userId)
activate Model
Model --> BackendController : user
deactivate Model

BackendController -> Model : setApproved(true)
activate Model
Model --> BackendController : user
deactivate Model

BackendController -> EmailService : sendApprovalEmail(user)
activate EmailService
EmailService --> BackendController : emailSent
deactivate EmailService

BackendController --> Controller : response
deactivate BackendController

alt errors.length==0
    Controller --> View : {success: true}
    View --> Administrateur : afficher "Utilisateur approuvé avec succès"
else
    Controller --> View : {success: false, error: message}
    View --> Administrateur : afficher les erreurs
end

deactivate Controller
deactivate View

== Rejeter un utilisateur ==

Administrateur -> View : Cliquer sur "Rejeter"
View --> Administrateur : afficher boîte de dialogue avec champ de raison
Administrateur -> View : Saisir la raison et confirmer
activate View

View -> Controller : rejectUser(userId, reason)
activate Controller

Controller -> BackendController : POST /api/admin/users/reject/{id}
activate BackendController

BackendController -> Model : find(userId)
activate Model
Model --> BackendController : user
deactivate Model

BackendController -> EmailService : sendRejectionEmail(user, reason)
activate EmailService
EmailService --> BackendController : emailSent
deactivate EmailService

BackendController -> Model : delete(user)
activate Model
Model --> BackendController : deleted
deactivate Model

BackendController --> Controller : response
deactivate BackendController

alt errors.length==0
    Controller --> View : {success: true}
    View --> Administrateur : afficher "Utilisateur rejeté avec succès"
else
    Controller --> View : {success: false, error: message}
    View --> Administrateur : afficher les erreurs
end

deactivate Controller
deactivate View

@enduml
