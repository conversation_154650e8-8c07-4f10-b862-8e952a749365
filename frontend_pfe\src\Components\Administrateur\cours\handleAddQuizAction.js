// Fonction corrigée pour ajouter une action à un quiz
const handleAddQuizAction = async (courseId, quizId) => {
  console.log("Ajout d'une action pour le quiz:", quizId);
  try {
    // Trouver le quiz pour obtenir son ID réel
    let quiz = null;

    for (const course of courses) {
      for (const q of course.quizzes || []) {
        if (q.IDModule === quizId) {
          quiz = q;
          break;
        }
      }
      if (quiz) break;
    }

    if (!quiz) {
      throw new Error(`Quiz avec ID ${quizId} non trouvé`);
    }

    // Créer une nouvelle action avec des valeurs par défaut
    const newAction = {
      id: Date.now(), // ID temporaire pour l'interface
      Action_Nom_FR: "Nouvelle Action",
      Action_Nom_EN: "New Action",
      Action_Categorie_FR: "",
      Action_Categorie_EN: "",
    };

    // Mettre à jour l'état local d'abord pour une meilleure expérience utilisateur
    setCourses(
      courses.map((course) => ({
        ...course,
        quizzes:
          course.quizzes?.map((q) =>
            q.IDModule === quizId
              ? {
                  ...q,
                  actions: [...(q.actions || []), newAction],
                }
              : q
          ) || [],
      }))
    );

    // Appeler l'API pour créer l'action en utilisant la route correcte
    console.log("Données envoyées pour createAction:", {
      IDModule: quizId,
      Action_Nom_FR: newAction.Action_Nom_FR,
      Action_Nom_EN: newAction.Action_Nom_EN,
      Action_Categorie_FR: newAction.Action_Categorie_FR || "",
      Action_Categorie_EN: newAction.Action_Categorie_EN || "",
    });

    const response = await fetch(`${API_BASE_URL}/quiz/action/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        IDModule: quizId, // Utiliser IDModule comme attendu par QuizController
        Action_Nom_FR: newAction.Action_Nom_FR,
        Action_Nom_EN: newAction.Action_Nom_EN,
        Action_Categorie_FR: newAction.Action_Categorie_FR || "",
        Action_Categorie_EN: newAction.Action_Categorie_EN || "",
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Erreur API:", response.status, errorText);
      throw new Error(`Erreur ${response.status}: ${errorText}`);
    }

    const responseData = await response.json();
    console.log("Action créée avec succès:", responseData);

    // Recharger les données complètes pour s'assurer que nous avons les dernières informations
    const refreshedCourses = await fetchCourses();
    setCourses(refreshedCourses);

    setDialog({
      show: true,
      title: "Succès",
      message: "Action ajoutée avec succès",
      type: "success",
      onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
      confirmText: "OK",
    });
  } catch (err) {
    console.error("Erreur lors de l'ajout de l'action:", err);
    setDialog({
      show: true,
      title: "Erreur",
      message: `Échec de l'ajout: ${err.message}`,
      type: "error",
      onConfirm: () => setDialog((prev) => ({ ...prev, show: false })),
      confirmText: "OK",
    });
  }
};
