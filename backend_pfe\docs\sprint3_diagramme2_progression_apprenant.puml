@startuml Diagramme de Séquence - Conception Sprint 3 (Évaluation et Suivi des Apprenants et Certifications) - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F
actor "Apprenant" as A
actor "Administrateur" as Admin

participant "«View»\nQuizDetailsView" as QDV
participant "«View»\nApprenantCoursView" as ACV
participant "«View»\nDashboardView" as DV
participant "«View»\nFormateurDashboardView" as FDV
participant "«View»\nCertificateDisplayView" as CDV
participant "«View»\nCourseManagementView" as CMV
participant "«View»\nCalendarPageView" as CPV

participant "«Controller»\nEvaluationController" as EC
participant "«Controller»\nProgressionController" as PC
participant "«Controller»\nCertificatController" as CC
participant "«Controller»\nDashboardController" as DC
participant "«Controller»\nEvenementController" as EVC

participant "«Model»\nEvaluation" as EM
participant "«Model»\nProgression" as PM
participant "«Model»\nCertificat" as CM
participant "«Model»\nApprenant" as AM
participant "«Model»\nFormateur" as FM
participant "«Model»\nQuiz" as QM
participant "«Model»\nCours" as CoM
participant "«Model»\nEvenement" as EveM
participant "«Service»\nEmailService" as ES

== Sélection d'un cours d'un apprenant par le formateur ==

F -> QDV : Cliquer sur "Sélectionner un apprenant"
QDV -> ACV : Naviguer vers la liste des apprenants
ACV -> PC : getProgressionByApprenant(apprenantId)
PC -> AM : findApprenantById(apprenantId)
AM --> PC : apprenant
PC -> PM : findProgressionsByApprenant(apprenant)
PM --> PC : progressions[]
PC --> ACV : afficher liste des cours de l'apprenant
ACV --> F : afficher cours disponibles

F -> ACV : Sélectionner un cours spécifique
ACV -> QDV : Naviguer vers les détails du quiz
QDV -> EC : getEvaluationByQuizAndApprenant(quizId, apprenantId)
EC -> QM : findQuizById(quizId)
QM --> EC : quiz
EC -> AM : findApprenantById(apprenantId)
AM --> EC : apprenant
EC -> EM : findEvaluationByQuizAndApprenant(quiz, apprenant)
EM --> EC : evaluation
EC --> QDV : afficher détails du quiz et évaluation existante

== Attribution d'un statut "Satisfaisant" ou "Non Satisfaisant" ==

F -> QDV : Évaluer les compétences du quiz
QDV -> QDV : Vérifier que toutes les actions sont cochées
QDV -> QDV : Activer le bouton "Satisfaisant" si toutes actions validées

F -> QDV : Cliquer sur "Satisfaisant" ou "Non Satisfaisant"
QDV -> EC : create(evaluationData)
EC -> EM : new Evaluation()
EM -> EM : setStatutEvaluation(statut)
EM -> EM : setQuiz(quiz)
EM -> EM : setFormateur(formateur)
EM -> EM : setApprenant(apprenant)
EM -> EM : synchronizeIdmodule()
EC -> EM : persist(evaluation)

EC -> PM : findOrCreateProgression(cours, evaluation, apprenant)
PM -> PM : updateProgression()
EC -> PM : persist(progression)

alt Statut = "Satisfaisant"
    EC -> EC : checkAndGenerateCertificateIfNeeded(apprenant, cours)
    EC -> CC : checkAllQuizzesSatisfaisant(apprenant, cours)
    CC -> EM : findEvaluationsByCours(cours, apprenant)
    EM --> CC : evaluations[]
    CC -> CC : verifyAllSatisfaisant(evaluations)

    alt Tous les quiz sont "Satisfaisant"
        CC -> CM : generateCertificate(apprenant, cours)
        CM -> CM : new Certificat()
        CM -> CM : setApprenant(apprenant)
        CM -> CM : setProgression(progression)
        CM -> CM : setIsAutoGenerated(true)
        CC -> CM : persist(certificat)
        CC --> EC : certificateInfo
    end
end

EC -> ES : sendApprovalEmail(apprenant, evaluation)
ES --> EC : emailSent
EC --> QDV : success response avec certificat si généré
QDV --> F : afficher confirmation d'évaluation

== Visualisation de la progression par l'apprenant ==

A -> DV : Accéder au tableau de bord
DV -> PC : getProgressionByApprenant(apprenantId)
PC -> AM : findApprenantById(apprenantId)
AM --> PC : apprenant
PC -> PM : findProgressionsByApprenant(apprenant)
PM --> PC : progressions[]
PC -> CoM : findCoursByApprenant(apprenant)
CoM --> PC : cours[]
PC -> EM : findEvaluationsByApprenant(apprenant)
EM --> PC : evaluations[]
PC -> PC : calculateOverallProgress(progressions, evaluations)
PC --> DV : progressionData avec pourcentages
DV --> A : afficher tableau de bord avec progression

A -> DV : Consulter détails d'un cours
DV -> PC : getProgressionByApprenantAndCours(apprenantId, coursId)
PC -> PM : findProgressionByCours(apprenant, cours)
PM --> PC : progression
PC -> CC : checkCertificateAvailability(apprenant, cours)
CC -> CM : findCertificatByApprenantAndCours(apprenant, cours)
CM --> CC : certificat ou null
CC --> PC : certificateStatus
PC --> DV : détails progression avec statut certificat
DV --> A : afficher progression détaillée

== Consultation des statistiques par administrateur/formateur ==

Admin -> FDV : Accéder au dashboard administrateur
FDV -> DC : getStats()
DC -> AM : countApprenants()
AM --> DC : totalApprenants
DC -> CoM : countCours()
CoM --> DC : totalCours
DC -> EM : countEvaluations()
EM --> DC : totalEvaluations
DC -> CM : countCertificats()
CM --> DC : totalCertificats
DC -> DC : calculateGrowthRates()
DC --> FDV : statistiques complètes
FDV --> Admin : afficher dashboard avec stats

F -> FDV : Accéder au dashboard formateur
FDV -> DC : getFormateurStats()
DC -> FM : findFormateurByUser(user)
FM --> DC : formateur
DC -> EM : findEvaluationsByFormateur(formateur)
EM --> DC : evaluations[]
DC -> DC : calculateFormateurStats(evaluations)
DC --> FDV : statistiques formateur
FDV --> F : afficher dashboard formateur

== Génération et téléchargement de certificat ==

A -> CDV : Cliquer sur "Générer certificat"
CDV -> CC : generateDirect(apprenantId, coursId)
CC -> AM : findApprenantById(apprenantId)
AM --> CC : apprenant
CC -> CoM : findCoursById(coursId)
CoM --> CC : cours
CC -> PM : findProgressionByCours(apprenant, cours)
PM --> CC : progression

alt Progression = 100%
    CC -> CM : new Certificat()
    CM -> CM : setApprenant(apprenant)
    CM -> CM : setProgression(progression)
    CM -> CM : setDateObtention(now)
    CM -> CM : setIsAutoGenerated(false)
    CC -> CM : persist(certificat)
    CC --> CDV : certificat généré avec succès
    CDV --> A : afficher certificat
else Progression < 100%
    CC --> CDV : erreur "Progression insuffisante"
    CDV --> A : afficher message d'erreur
end

A -> CDV : Cliquer sur "Télécharger PDF"
CDV -> CC : downloadCertificatPDF(certificatData)
CC -> CC : generatePDFContent(certificat)
CC --> CDV : fichier PDF
CDV --> A : télécharger certificat.pdf

== Consultation des certificats délivrés par l'administrateur ==

Admin -> CMV : Accéder à la gestion des certificats
CMV -> CC : getAllCertificats()
CC -> CM : findAllCertificats()
CM --> CC : certificats[]
CC -> AM : getApprenantDetails(certificats)
AM --> CC : apprenants[]
CC -> CoM : getCoursDetails(certificats)
CoM --> CC : cours[]
CC --> CMV : liste complète des certificats
CMV --> Admin : afficher liste des certificats délivrés

Admin -> CMV : Filtrer par apprenant
CMV -> CC : getCertificatsByApprenant(apprenantId)
CC -> CM : findCertificatsByApprenant(apprenantId)
CM --> CC : certificats filtrés
CC --> CMV : certificats de l'apprenant
CMV --> Admin : afficher certificats filtrés

Admin -> CMV : Filtrer par cours
CMV -> CC : getCertificatsByCours(coursId)
CC -> CM : findCertificatsByCours(coursId)
CM --> CC : certificats filtrés
CC --> CMV : certificats du cours
CMV --> Admin : afficher certificats filtrés

Admin -> CMV : Consulter détails d'un certificat
CMV -> CC : getCertificatDetails(certificatId)
CC -> CM : findCertificatById(certificatId)
CM --> CC : certificat
CC -> AM : getApprenantByCertificat(certificat)
AM --> CC : apprenant
CC -> CoM : getCoursByCertificat(certificat)
CoM --> CC : cours
CC -> PM : getProgressionByCertificat(certificat)
PM --> CC : progression
CC --> CMV : détails complets du certificat
CMV --> Admin : afficher informations détaillées

== Création d'événement d'évaluation par l'administrateur ==

Admin -> CPV : Accéder au calendrier des évaluations
CPV -> EVC : getEvenements()
EVC -> EveM : findAllEvenements()
EveM --> EVC : evenements[]
EVC --> CPV : liste des événements
CPV --> Admin : afficher calendrier

Admin -> CPV : Cliquer sur "Créer une évaluation"
CPV -> CPV : Afficher formulaire de création
Admin -> CPV : Saisir détails de l'événement (titre, description, date)
CPV -> EVC : createEvenement(evenementData)
EVC -> EveM : new Evenement()
EveM -> EveM : setTitre(titre)
EveM -> EveM : setDescription(description)
EveM -> EveM : setDateDebut(dateDebut)
EveM -> EveM : setCategorie("evaluation")
EveM -> EveM : setCouleur("#EA4335")
EVC -> EveM : persist(evenement)

EVC -> AM : findAllApprenants()
AM --> EVC : apprenants[]
loop Pour chaque apprenant approuvé
    EVC -> EVC : createNotification(apprenant, evenement)
    EVC -> EVC : persist(notification)
end

EVC -> ES : sendEventNotifications(apprenants, evenement)
ES --> EVC : notifications envoyées
EVC --> CPV : événement créé avec succès
CPV --> Admin : afficher confirmation et mettre à jour calendrier

note right of EVC : Toutes les notifications sont\nenvoyées automatiquement\naux apprenants approuvés

@enduml
