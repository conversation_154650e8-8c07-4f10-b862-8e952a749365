@startuml Sprint 3 - Diagramme de Séquence Système (Évaluation et Suivi)

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F
actor "Apprenant" as A
actor "Administrateur" as Admin
participant "Système" as S

== Sélection et Évaluation d'un Cours d'Apprenant ==

group Sélectionner un cours d'un apprenant
    F -> S : Accéder à la liste des apprenants
    S --> F : Afficher la liste des apprenants
    F -> S : Sélectionner un apprenant
    S --> F : Afficher les cours de l'apprenant
    F -> S : Choisir un cours à évaluer
    S --> F : Afficher les détails du cours (quiz, compétences, actions)
end

group Attribuer un statut d'évaluation
    F -> S : Consulter les compétences du quiz
    S --> F : Afficher les compétences avec leurs statuts possibles

    loop Pour chaque compétence
        F -> S : Attribuer un statut à la compétence

        alt Compétence acquise
            F -> S : Marquer comme "Acquise"
            S --> F : Mettre à jour le statut en vert

        else Compétence à améliorer
            F -> S : Marquer comme "À améliorer"
            S --> F : Mettre à jour le statut en orange
            F -> S : Cocher les sous-compétences maîtrisées
            S --> F : Enregistrer les sous-compétences cochées

        else Compétence non acquise
            F -> S : Marquer comme "Non acquise"
            S --> F : Mettre à jour le statut en rouge
        end

        S -> S : Sauvegarder le statut de la compétence
    end

    F -> S : Consulter les actions du quiz
    S --> F : Afficher les actions avec cases à cocher

    loop Pour chaque action
        F -> S : Marquer/démarquer l'action comme complétée
        S --> F : Mettre à jour l'état de l'action en temps réel
        S -> S : Sauvegarder l'état de l'action
    end

    opt Quiz avec MainSurface = 1
        F -> S : Saisir les valeurs Main et Surface
        S --> F : Afficher les champs éditables
        F -> S : Valider les valeurs saisies
        S -> S : Enregistrer les valeurs Main et Surface
    end

    F -> S : Vérifier la complétude de l'évaluation
    S -> S : Vérifier que toutes les compétences sont évaluées
    S -> S : Vérifier que toutes les actions sont cochées

    alt Toutes les compétences évaluées ET toutes les actions complétées
        F -> S : Attribuer statut "Satisfaisant"
        S --> F : Confirmer l'attribution du statut
        S -> S : Enregistrer tous les détails d'évaluation
        S -> S : Calculer la progression (100%)

        opt Progression = 100%
            S -> S : Générer automatiquement le certificat
            S -> A : Envoyer notification de certificat disponible
        end

    else Compétences non évaluées OU actions incomplètes
        F -> S : Attribuer statut "Non Satisfaisant"
        S --> F : Confirmer l'attribution du statut
        S -> S : Enregistrer les détails d'évaluation partiels
        S -> S : Enregistrer les statuts des compétences évaluées
        S -> S : Enregistrer les actions complétées

        note right of S : L'apprenant devra reprendre\nles éléments non satisfaisants
    end

    S -> S : Enregistrer l'évaluation complète
end

== Visualisation de la Progression ==

group Tableau de bord apprenant
    A -> S : Accéder au tableau de bord
    S --> A : Afficher la page de connexion si non connecté
    A -> S : Se connecter
    S -> S : Vérifier les identifiants

    alt Identifiants valides
        alt Compte approuvé
            S --> A : Afficher le tableau de bord
            S -> S : Calculer la progression des cours
            S --> A : Afficher les cours et pourcentages de progression

            opt Certificat disponible
                S --> A : Afficher le bouton de téléchargement du certificat
            end

        else Compte non approuvé
            S --> A : Afficher "Votre compte est en attente d'approbation"
        end

    else Identifiants invalides
        S --> A : Afficher "Identifiants invalides"
    end
end

== Gestion des Certificats ==

group Téléchargement de certificat
    A -> S : Cliquer sur "Télécharger le certificat"
    S -> S : Vérifier l'existence du certificat

    alt Certificat existe
        S -> S : Générer le PDF du certificat
        S --> A : Télécharger le fichier PDF

    else Certificat inexistant
        S --> A : Afficher "Certificat non disponible"
    end
end

group Consultation des certificats délivrés (Administrateur)
    Admin -> S : Accéder à la gestion des certificats
    S --> Admin : Afficher la liste des certificats délivrés
    Admin -> S : Filtrer par apprenant ou cours
    S --> Admin : Afficher les certificats filtrés
    Admin -> S : Consulter les détails d'un certificat
    S --> Admin : Afficher les informations du certificat
end

== Statistiques et Événements ==

group Statistiques des évaluations
    alt Formateur
        F -> S : Accéder aux statistiques
        S --> F : Afficher les statistiques de ses évaluations

    else Administrateur
        Admin -> S : Accéder aux statistiques globales
        S --> Admin : Afficher toutes les statistiques d'évaluation
    end
end

group Création d'événement d'évaluation
    Admin -> S : Accéder au calendrier des événements
    S --> Admin : Afficher le calendrier
    Admin -> S : Créer un nouvel événement
    S --> Admin : Afficher le formulaire de création
    Admin -> S : Saisir les détails de l'événement
    Admin -> S : Valider la création
    S -> S : Enregistrer l'événement
    S -> A : Envoyer notification aux apprenants
    S -> F : Envoyer notification aux formateurs
    S --> Admin : Confirmer la création de l'événement
end

@enduml
