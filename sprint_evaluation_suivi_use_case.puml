@startuml Sprint Evaluation et Suivi des Apprenants - Use Case Diagram

!define RECTANGLE class

' Définition des acteurs
actor Formateur as F
actor Apprenant as A
actor Administrateur as Admin

' Package principal du système
package "Système PharmaLearn - Sprint Évaluation et Suivi" {
    
    ' Use cases principaux du sprint
    usecase "Sélectionner un cours d'un apprenant" as UC1
    usecase "Attribuer un statut d'évaluation" as UC2
    usecase "Visualiser progression sur tableau de bord" as UC3
    usecase "Voir statistiques des évaluations" as UC4
    usecase "Obtenir certificat automatiquement" as UC5
    usecase "Télécharger certificat en PDF" as UC6
    usecase "Voir certificats délivrés" as UC7
    usecase "Créer événement d'évaluation" as UC8
    
    ' Use cases de support/inclusion
    usecase "Marquer statut Satisfaisant" as UC2a
    usecase "Marquer statut Non Satisfaisant" as UC2b
    usecase "Calculer progression du cours" as UC3a
    usecase "Afficher actions complétées" as UC3b
    usecase "Générer certificat automatiquement" as UC5a
    usecase "Vérifier taux de réussite 100%" as UC5b
    usecase "Envoyer notification certificat" as UC5c
    usecase "Consulter historique évaluations" as UC4a
    usecase "Filtrer évaluations par période" as UC4b
    usecase "Exporter statistiques" as UC4c
    usecase "Valider format PDF" as UC6a
    usecase "Gérer téléchargement sécurisé" as UC6b
    usecase "Lister tous les certificats" as UC7a
    usecase "Filtrer certificats par apprenant" as UC7b
    usecase "Planifier évaluation" as UC8a
    usecase "Notifier participants" as UC8b
    
    ' Use cases système/entités
    usecase "Gérer entité Evaluation" as SYS1
    usecase "Gérer entité Progression" as SYS2
    usecase "Gérer entité Certificat" as SYS3
    usecase "Gérer entité Notification" as SYS4
}

' Relations acteurs - use cases principaux
F --> UC1 : "Sélectionne"
F --> UC2 : "Attribue"
F --> UC4 : "Consulte"

A --> UC3 : "Visualise"
A --> UC5 : "Obtient"
A --> UC6 : "Télécharge"

Admin --> UC4 : "Consulte"
Admin --> UC7 : "Supervise"
Admin --> UC8 : "Crée"

' Relations include (dépendances obligatoires)
UC2 ..> UC2a : <<include>>
UC2 ..> UC2b : <<include>>
UC2 ..> SYS1 : <<include>>

UC3 ..> UC3a : <<include>>
UC3 ..> UC3b : <<include>>
UC3 ..> SYS2 : <<include>>

UC4 ..> UC4a : <<include>>
UC4 ..> UC4b : <<include>>

UC5 ..> UC5a : <<include>>
UC5 ..> UC5b : <<include>>
UC5 ..> SYS3 : <<include>>

UC6 ..> UC6a : <<include>>
UC6 ..> UC6b : <<include>>

UC7 ..> UC7a : <<include>>
UC7 ..> UC7b : <<include>>

UC8 ..> UC8a : <<include>>
UC8 ..> UC8b : <<include>>
UC8 ..> SYS4 : <<include>>

' Relations extend (fonctionnalités optionnelles)
UC4c ..> UC4 : <<extend>>
UC5c ..> UC5 : <<extend>>

' Relations entre entités système
SYS1 --> SYS2 : "Met à jour"
SYS2 --> SYS3 : "Déclenche génération"
SYS3 --> SYS4 : "Génère notification"

' Notes explicatives
note right of UC2
  Statut: "Satisfaisant" ou 
  "Non Satisfaisant"
  Associé à un quiz spécifique
end note

note right of UC5
  Certificat généré automatiquement
  quand progression = 100%
  Toutes les actions cochées
end note

note right of UC4
  Statistiques disponibles pour
  Administrateur ET Formateur
end note

note bottom of UC8
  Événement d'évaluation avec
  notifications automatiques
  aux participants concernés
end note

@enduml
