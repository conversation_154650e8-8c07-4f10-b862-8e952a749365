@startuml
title Diagramme de séquence système - Approuver/Rejeter les demandes d'inscription

actor Administrateur
participant Système

== Approuver/Rejeter les demandes d'inscription ==

ref over Administrateur, Système
    Approuver/Rejeter les demandes d'inscription
end ref

Administrateur -> Système : Accéder à la page des demandes d'inscription
Système -> Système : Récupérer la liste des utilisateurs en attente
Système --> Administrateur : Afficher la liste des demandes

alt [Approuver un utilisateur]
    Administrateur -> Système : Cliquer sur "Approuver" pour un utilisateur
    Système --> Administrateur : Afficher une boîte de dialogue de confirmation
    Administrateur -> Système : Confirmer l'approbation
    Système -> Système : Marquer l'utilisateur comme approuvé
    Système -> Système : Envoyer un email de confirmation à l'utilisateur
    Système --> Administrateur : Afficher "Utilisateur approuvé avec succès"
    Système --> Administrateur : Mettre à jour la liste des demandes
else [Rejeter un utilisateur]
    Administrateur -> Système : Cliquer sur "Rejeter" pour un utilisateur
    Système --> Administrateur : Afficher une boîte de dialogue avec champ de raison
    Administrateur -> Système : Saisir la raison du rejet
    Administrateur -> Système : Confirmer le rejet
    Système -> Système : Envoyer un email de rejet à l'utilisateur avec la raison
    Système -> Système : Supprimer l'utilisateur de la base de données
    Système --> Administrateur : Afficher "Utilisateur rejeté avec succès"
    Système --> Administrateur : Mettre à jour la liste des demandes
end

@enduml
