@startuml Sprint4_Conception_Class_Diagram_Corrected

!theme plain
skinparam backgroundColor #FAFAFA
skinparam class {
    BackgroundColor White
    BorderColor #333333
    ArrowColor #333333
    FontSize 10
}

skinparam package {
    BackgroundColor #E8F4FD
    BorderColor #1976D2
    FontStyle bold
}

title "Sprint 4 - Diagramme de Classes de Conception Corrigé\nMessagerie et Notifications"

' ===== COUCHE VIEW (Frontend React) =====
package "«View»" #E8F4FD {

    class MessagerieView <<View>> {
        - messages: Array
        - newMessage: string
        - loading: boolean
        --
        + sendMessage(): void
        + fetchMessages(): void
        + markAsRead(): void
    }

    class NotificationCenterView <<View>> {
        - notifications: Array
        - unreadCount: int
        --
        + fetchNotifications(): void
        + markAsRead(): void
        + markAllAsRead(): void
    }

    class ReclamationView <<View>> {
        - reclamations: Array
        - newReclamation: Object
        --
        + handleSubmit(): void
        + handleReply(): void
    }

    class EvaluationView <<View>> {
        - evaluation: string
        - alreadyEvaluated: boolean
        --
        + handleEvaluation(): void
        + checkEvaluationConditions(): void
    }

    class CertificateView <<View>> {
        - certificateData: Object
        --
        + handleDownload(): void
        + generateCertificateContent(): void
    }

    class CalendarView <<View>> {
        - events: Array
        - selectedEvent: Object
        --
        + handleCreateEvent(): void
        + handleUpdateEvent(): void
        + handleDeleteEvent(): void
    }
}

' ===== COUCHE CONTROLLER (Backend Symfony) =====
package "«Controller»" #FFF3E0 {

    class MessagerieController <<Controller>> {
        --
        + getConversation(formateurId: int, apprenantId: int): JsonResponse
        + getFormateurConversations(formateurId: int): JsonResponse
        + getApprenantConversations(apprenantId: int): JsonResponse
        + formateurEnvoyerMessage(request: Request, formateurId: int, apprenantId: int): JsonResponse
        + apprenantEnvoyerMessage(request: Request, apprenantId: int, formateurId: int): JsonResponse
        + marquerLu(id: int): JsonResponse
        + getFormateursForApprenant(apprenantId: int): JsonResponse
        + getApprenantsForFormateur(formateurId: int): JsonResponse
    }

    class NotificationController <<Controller>> {
        --
        + getUserNotifications(request: Request): JsonResponse
        + markAsRead(id: int): JsonResponse
        + markAllAsRead(): JsonResponse
        + deleteNotification(id: int): JsonResponse
    }

    class ReclamationController <<Controller>> {
        --
        + list(): JsonResponse
        + getUserReclamations(): JsonResponse
        + show(id: int): JsonResponse
        + create(request: Request): JsonResponse
        + reply(id: int, request: Request): JsonResponse
    }

    class EvaluationController <<Controller>> {
        --
        + list(): JsonResponse
        + show(id: int): JsonResponse
        + create(request: Request): JsonResponse
        + getEvaluationsByIdmodule(idmodule: string): JsonResponse
        + getEvaluationByIdmoduleAndApprenant(idmodule: string, apprenantId: int): JsonResponse
        + getEvaluationByQuizAndApprenant(quizId: int, apprenantId: int): JsonResponse
        - checkAndGenerateCertificateIfNeeded(apprenant: Apprenant, cours: Cours): array
    }

    class CertificatController <<Controller>> {
        --
        + list(): JsonResponse
        + checkAndGenerate(apprenantId: int, coursId: int): JsonResponse
        + show(id: int): JsonResponse
        + generateDirect(request: Request): JsonResponse
        + download(id: int): Response
        + getCertificatsByApprenant(apprenantId: int): JsonResponse
    }

    class EvenementController <<Controller>> {
        --
        + debug(): JsonResponse
        + list(request: Request): JsonResponse
        + show(id: int): JsonResponse
        + create(request: Request): JsonResponse
        + update(request: Request, id: int): JsonResponse
        + delete(id: int): JsonResponse
        + getByAdministrateur(id: int): JsonResponse
        + getByDateRange(request: Request): JsonResponse
        + getUpcoming(request: Request): JsonResponse
    }
}

' ===== COUCHE MODEL (Entités Backend) =====
package "«Model»" #E8F5E8 {

    class Messagerie <<Model>> {
        - id: int
        - message: string
        - lu: boolean
        - date: DateTime
        - sentByFormateur: boolean
        --
        + setLu(lu: boolean): self
        + isSentByFormateur(): boolean
        + setSentByFormateur(sentByFormateur: boolean): self
        + addNotification(notification: Notification): self
        + removeNotification(notification: Notification): self
    }

    class Notification <<Model>> {
        - id: int
        - Description: string
        - read: boolean
        - createdAt: DateTimeImmutable
        - type: string
        --
        + setRead(read: boolean): self
        + isRead(): boolean
        + determineType(): string
        + toWebSocketArray(): array
        + setDescription(description: string): self
        + setUser(user: Utilisateur): self
        + setType(type: string): self
        + setCreatedAt(createdAt: DateTimeImmutable): self
    }

    class Reclamation <<Model>> {
        - id: int
        - subject: string
        - message: string
        - status: string
        - date: DateTime
        - response: string
        - responses: array
        - responseDate: DateTime
        --
        + addResponse(response: string, senderName: string, date: DateTime): self
        + setStatus(status: string): self
        + setSubject(subject: string): self
        + setMessage(message: string): self
        + setUser(user: Utilisateur): self
        + addNotification(notification: Notification): self
        + removeNotification(notification: Notification): self
    }

    class Evaluation <<Model>> {
        - id: int
        - statutEvaluation: string
        - idmodule: string
        - createdAt: DateTime
        --
        + setStatutEvaluation(statut: string): self
        + getStatutEvaluation(): string
        + setQuiz(quiz: Quiz): self
        + setFormateur(formateur: Formateur): self
        + setApprenant(apprenant: Apprenant): self
        + addNotification(notification: Notification): self
        + removeNotification(notification: Notification): self
    }

    class Certificat <<Model>> {
        - id: int
        - dateObtention: DateTime
        - contenu: string
        - isAutoGenerated: boolean
        --
        + setIsAutoGenerated(isAutoGenerated: boolean): self
        + setProgression(progression: Progression): self
        + setApprenant(apprenant: Apprenant): self
        + addNotification(notification: Notification): self
        + removeNotification(notification: Notification): self
    }

    class Evenement <<Model>> {
        - id: int
        - titre: string
        - description: string
        - dateDebut: DateTime
        - dateFin: DateTime
        - journeeEntiere: boolean
        - categorie: string
        - couleur: string
        --
        + setJourneeEntiere(journeeEntiere: boolean): self
        + addAdministrateur(administrateur: Administrateur): self
        + removeAdministrateur(administrateur: Administrateur): self
        + addNotification(notification: Notification): self
        + removeNotification(notification: Notification): self
    }

    class Utilisateur <<Model>> {
        - id: int
        - name: string
        - email: string
        - role: string
        - isApproved: boolean
        --
        + setIsApproved(isApproved: boolean): self
        + hasRole(role: string): boolean
    }
}

' ===== RELATIONS =====

' Relations Views vers Controllers
MessagerieView --> MessagerieController : utilise
NotificationCenterView --> NotificationController : utilise
ReclamationView --> ReclamationController : utilise
EvaluationView --> EvaluationController : utilise
CertificateView --> CertificatController : utilise
CalendarView --> EvenementController : utilise

' Relations Controllers vers Models
MessagerieController --> Messagerie : manipule
MessagerieController --> Notification : manipule

NotificationController --> Notification : manipule

ReclamationController --> Reclamation : manipule
ReclamationController --> Notification : manipule

EvaluationController --> Evaluation : manipule
EvaluationController --> Certificat : manipule
EvaluationController --> Notification : manipule

CertificatController --> Certificat : manipule
CertificatController --> Notification : manipule

EvenementController --> Evenement : manipule
EvenementController --> Notification : manipule

' Relations entre Models - Notification au centre
Notification "0..*" --> "0..1" Messagerie : messagerie
Notification "0..*" --> "0..1" Reclamation : reclamation
Notification "0..*" --> "0..1" Certificat : certificat
Notification "0..*" --> "0..1" Evaluation : evaluation
Notification "0..*" --> "0..1" Evenement : evenement
Notification "0..*" --> "1" Utilisateur : user

' Relations principales
Messagerie "0..*" --> "1" Utilisateur : formateur
Messagerie "0..*" --> "1" Utilisateur : apprenant

Reclamation "0..*" --> "1" Utilisateur : user

Evaluation "0..*" --> "1" Utilisateur : formateur
Evaluation "0..*" --> "1" Utilisateur : apprenant

Certificat "0..*" --> "1" Utilisateur : apprenant

Evenement "0..*" --> "0..*" Utilisateur : administrateurs

@enduml
