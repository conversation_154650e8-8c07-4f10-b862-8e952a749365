@startuml Sous-Competence Update Sequence Design

title Diagramme de Séquence - Modification d'une sous-compétence

actor Administrateur
participant "«View»\nSousCompetenceForm.jsx" as View
participant "«Controller»\nSousCompetenceController" as Controller
participant "«Model»\nSousCompetence" as Model

Administrateur -> View : Cliquer sur "Modifier" pour une sous-compétence
View -> Controller : GET /api/sous_competence/{id}
Controller -> Model : Récupérer la sous-compétence
Model --> Controller : Donn<PERSON> de la sous-compétence
Controller --> View : Afficher le formulaire pré-rempli

Administrateur -> View : Modifier les champs (nom_fr, nom_en)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : PUT /api/sous_competence/{id}
Controller -> Model : Mettre à jour la sous-compétence
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : <PERSON><PERSON>iche<PERSON> le résultat

@enduml
