@startuml Diagramme de Séquence de Conception - Réinitialisation de mot de passe

actor Utilisateur
participant "<<View>>\nForgotPassword.jsx" as ForgotPwdView
participant "<<View>>\nResetPassword.jsx" as ResetPwdView
participant "<<ViewModel>>\nAuthContext" as AuthContext
participant "<<Model>>\nAuthController" as AuthController
participant "<<Model>>\nUtilisateur" as UtilisateurModel
participant "<<Model>>\nEmailService" as EmailService

title Diagramme de Séquence de Conception - Réinitialisation de mot de passe

ref over Utilisateur, EmailService : Réinitialiser le mot de passe

== Demande de réinitialisation ==

Utilisateur -> ForgotPwdView : Cliquer sur "Mot de passe oublié"
activate ForgotPwdView

ForgotPwdView -> ForgotPwdView : Valider l'email
ForgotPwdView -> AuthContext : forgotPassword(email)
activate AuthContext

AuthContext -> AuthController : POST /api/forgot-password
activate AuthController

AuthController -> UtilisateurModel : findByEmail(email)
activate UtilisateurModel
UtilisateurModel --> AuthController : utilisateur
deactivate UtilisateurModel

alt utilisateur != null
    AuthController -> AuthController : generateResetToken()
    AuthController -> UtilisateurModel : setResetToken(token)
    activate UtilisateurModel
    UtilisateurModel --> AuthController : utilisateur
    deactivate UtilisateurModel
    
    AuthController -> EmailService : sendPasswordResetEmail(utilisateur, token)
    activate EmailService
    EmailService --> AuthController : emailSent
    deactivate EmailService
end

AuthController --> AuthContext : response
deactivate AuthController

AuthContext --> ForgotPwdView : {success: true}
ForgotPwdView --> Utilisateur : afficher "Un email a été envoyé si l'adresse existe"

deactivate AuthContext
deactivate ForgotPwdView

== Réinitialisation du mot de passe ==

Utilisateur -> ResetPwdView : Cliquer sur le lien dans l'email
activate ResetPwdView

ResetPwdView -> ResetPwdView : Extraire le token de l'URL
ResetPwdView --> Utilisateur : afficher le formulaire de nouveau mot de passe

Utilisateur -> ResetPwdView : Saisir le nouveau mot de passe
Utilisateur -> ResetPwdView : Cliquer sur "Enregistrer"

ResetPwdView -> ResetPwdView : Valider le mot de passe
ResetPwdView -> AuthContext : resetPassword(token, password)
activate AuthContext

AuthContext -> AuthController : POST /api/reset-password/{token}
activate AuthController

AuthController -> UtilisateurModel : findByResetToken(token)
activate UtilisateurModel
UtilisateurModel --> AuthController : utilisateur
deactivate UtilisateurModel

alt utilisateur != null && token valide
    AuthController -> AuthController : hashPassword(password)
    AuthController -> UtilisateurModel : setPassword(hashedPassword)
    activate UtilisateurModel
    UtilisateurModel --> AuthController : utilisateur
    deactivate UtilisateurModel
    
    AuthController -> UtilisateurModel : clearResetToken()
    activate UtilisateurModel
    UtilisateurModel --> AuthController : utilisateur
    deactivate UtilisateurModel
    
    AuthController --> AuthContext : {success: true}
    AuthContext --> ResetPwdView : {success: true}
    ResetPwdView --> Utilisateur : afficher "Mot de passe réinitialisé avec succès"
    ResetPwdView --> Utilisateur : rediriger vers la page de connexion
else
    AuthController --> AuthContext : {success: false, error: message}
    AuthContext --> ResetPwdView : {success: false, error: message}
    ResetPwdView --> Utilisateur : afficher "Token invalide ou expiré"
end

deactivate AuthController
deactivate AuthContext
deactivate ResetPwdView

@enduml
