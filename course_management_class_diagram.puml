@startuml Course Management Class Diagram

' Définition des classes du modèle
package "«Model»" {
  class Cours {
    +id: int
    +titre: String
    +description: String
    +quizzes: Array<Quiz>
    +getId(): int
    +getTitre(): String
    +setTitre(titre: String): void
    +getDescription(): String
    +setDescription(description: String): void
    +getQuizzes(): Array<Quiz>
    +addQuiz(quiz: Quiz): void
    +removeQuiz(quiz: Quiz): void
  }

  class Quiz {
    +id: int
    +IDModule: String
    +nomFR: String
    +nomEN: String
    +type: String
    +category: String
    +mainSurface: boolean
    +main: int
    +surface: int
    +cours: Cours
    +competences: Array<Competence>
    +actions: Array<Action>
    +getId(): int
    +getIDModule(): String
    +setIDModule(IDModule: String): void
    +getNomFR(): String
    +setNomFR(nomFR: String): void
    +getNomEN(): String
    +setNomEN(nomEN: String): void
    +getType(): String
    +setType(type: String): void
    +getCategory(): String
    +setCategory(category: String): void
    +isMainSurface(): boolean
    +setMainSurface(mainSurface: boolean): void
    +getMain(): int
    +setMain(main: int): void
    +getSurface(): int
    +setSurface(surface: int): void
    +getCours(): Cours
    +setCours(cours: Cours): void
    +getCompetences(): Array<Competence>
    +addCompetence(competence: Competence): void
    +removeCompetence(competence: Competence): void
    +getActions(): Array<Action>
    +addAction(action: Action): void
    +removeAction(action: Action): void
  }

  class Competence {
    +id: int
    +idmodule: String
    +nomFr: String
    +nomEn: String
    +categorieFr: String
    +categorieEn: String
    +quiz: Quiz
    +sousCompetences: Array<SousCompetence>
    +getId(): int
    +getIdmodule(): String
    +setIdmodule(idmodule: String): void
    +getNomFr(): String
    +setNomFr(nomFr: String): void
    +getNomEn(): String
    +setNomEn(nomEn: String): void
    +getCategorieFr(): String
    +setCategorieFr(categorieFr: String): void
    +getCategorieEn(): String
    +setCategorieEn(categorieEn: String): void
    +getQuiz(): Quiz
    +setQuiz(quiz: Quiz): void
    +getSousCompetences(): Array<SousCompetence>
    +addSousCompetence(sousCompetence: SousCompetence): void
    +removeSousCompetence(sousCompetence: SousCompetence): void
    +synchronizeIdmodule(): void
  }

  class SousCompetence {
    +id: int
    +nomFr: String
    +nomEn: String
    +competence: Competence
    +getId(): int
    +getNomFr(): String
    +setNomFr(nomFr: String): void
    +getNomEn(): String
    +setNomEn(nomEn: String): void
    +getCompetence(): Competence
    +setCompetence(competence: Competence): void
  }

  class Action {
    +id: int
    +idmodule: String
    +nomFr: String
    +nomEn: String
    +categorieFr: String
    +categorieEn: String
    +quiz: Quiz
    +getId(): int
    +getIdmodule(): String
    +setIdmodule(idmodule: String): void
    +getNomFr(): String
    +setNomFr(nomFr: String): void
    +getNomEn(): String
    +setNomEn(nomEn: String): void
    +getCategorieFr(): String
    +setCategorieFr(categorieFr: String): void
    +getCategorieEn(): String
    +setCategorieEn(categorieEn: String): void
    +getQuiz(): Quiz
    +setQuiz(quiz: Quiz): void
    +synchronizeIdmodule(): void
  }
}

' Définition des classes du contrôleur
package "«Controller»" {
  class CourseController {
    -coursRepository: CoursRepository
    -entityManager: EntityManagerInterface
    +list(): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(request: Request, id: int): JsonResponse
    +delete(id: int): JsonResponse
  }

  class QuizController {
    -entityManager: EntityManagerInterface
    -coursRepository: CoursRepository
    -validator: ValidatorInterface
    +list(request: Request): JsonResponse
    +show(IDModule: String): JsonResponse
    +update(request: Request, IDModule: String): JsonResponse
    +delete(id: int): JsonResponse
    +deleteByIdModule(IDModule: String): JsonResponse
    +createBatch(request: Request): JsonResponse
    +updateCompetence(request: Request, IDModule: String, competenceId: String): JsonResponse
    +deleteCompetence(IDModule: String, competenceId: String): JsonResponse
    +updateSousCompetence(request: Request, IDModule: String, competenceId: String, sousCompetenceNomFR: String, sousCompetenceNomEN: String): JsonResponse
    +deleteSousCompetence(IDModule: String, competenceId: String, sousCompetenceNomFR: String, sousCompetenceNomEN: String): JsonResponse
    +updateQuizActionById(request: Request, id: int): JsonResponse
    +deleteQuizActionById(id: int): JsonResponse
    +createQuizAction(request: Request): JsonResponse
  }
}

' Définition des classes de la vue
package "«View»" {
  class CourseManagementPage {
    -courses: Array
    -loading: boolean
    -error: String
    -selectedCourse: Object
    -showAddModal: boolean
    -showEditModal: boolean
    -showDeleteModal: boolean
    +fetchCourses(): void
    +handleAddCourse(): void
    +handleEditCourse(course: Object): void
    +handleDeleteCourse(course: Object): void
    +handleSubmit(data: Object): void
    +render(): JSX
  }

  class QuizManagementPage {
    -quizzes: Array
    -loading: boolean
    -error: String
    -selectedQuiz: Object
    -showAddModal: boolean
    -showEditModal: boolean
    -showDeleteModal: boolean
    -showImportModal: boolean
    +fetchQuizzes(courseId: int): void
    +handleAddQuiz(): void
    +handleEditQuiz(quiz: Object): void
    +handleDeleteQuiz(quiz: Object): void
    +handleImportExcel(): void
    +handleExcelImport(file: File): void
    +handleSubmit(data: Object): void
    +render(): JSX
  }

  class CompetenceForm {
    -competence: Object
    -loading: boolean
    -error: String
    +handleChange(field: String, value: String): void
    +handleSubmit(): void
    +render(): JSX
  }

  class SousCompetenceForm {
    -sousCompetence: Object
    -competence: Object
    -loading: boolean
    -error: String
    +handleChange(field: String, value: String): void
    +handleSubmit(): void
    +render(): JSX
  }

  class ActionForm {
    -action: Object
    -quiz: Object
    -loading: boolean
    -error: String
    +handleChange(field: String, value: String): void
    +handleSubmit(): void
    +render(): JSX
  }
}

' Relations entre les classes
Cours "1" *-- "0..*" Quiz
Quiz "1" *-- "0..*" Competence
Quiz "1" *-- "0..*" Action
Competence "1" *-- "0..*" SousCompetence

CourseController --> Cours : manipule >
QuizController --> Quiz : manipule >
QuizController --> Competence : manipule >
QuizController --> SousCompetence : manipule >
QuizController --> Action : manipule >

CourseManagementPage --> CourseController : utilise >
QuizManagementPage --> QuizController : utilise >
CompetenceForm --> QuizController : utilise >
SousCompetenceForm --> QuizController : utilise >
ActionForm --> QuizController : utilise >

@enduml
