/* Styles personnalisés pour l'interface formateur */

/* Polices personnalisées */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap");

/* Styles de base */
.formateur-container {
  font-family:
    "Inter", "ui-sans-serif", "system-ui", "-apple-system",
    "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial",
    "sans-serif";
}

.formateur-title {
  font-family:
    "Inter", "ui-sans-serif", "system-ui", "-apple-system",
    "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial",
    "sans-serif";
  font-weight: medium;
}

/* Animations personnalisées */
.hover-lift {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Styles de cartes */
.formateur-card {
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
}

.formateur-card:hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Styles d'en-tête */
.formateur-header {
  transition-colors: 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Styles de boutons */
.btn-formateur-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 6px -1px rgba(37, 99, 235, 0.2),
    0 2px 4px -1px rgba(37, 99, 235, 0.1);
}

.btn-formateur-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow:
    0 6px 10px -1px rgba(37, 99, 235, 0.25),
    0 4px 6px -1px rgba(37, 99, 235, 0.15);
  transform: translateY(-1px);
}

.btn-formateur-secondary {
  background: white;
  color: #475569;
  font-weight: 500;
  padding: 0.5rem 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-formateur-secondary:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

/* Styles de tableaux de bord */
.dashboard-stat-card {
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  z-index: 1;
}

.dashboard-stat-card .stat-icon {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 1rem;
  display: inline-flex;
  backdrop-filter: blur(5px);
}

.dashboard-stat-card .stat-value {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

.dashboard-stat-card .stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 2;
}

/* Styles de profil */
.profile-menu {
  animation: scaleIn 0.2s ease-out;
  transform-origin: top right;
}

/* Styles de notifications */
.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #ef4444;
  border: 2px solid white;
}

.notification-item {
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Styles de recherche */
.search-input {
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Styles de mode sombre */
.dark .btn-formateur-secondary {
  background: #1e293b;
  color: #e2e8f0;
  border-color: #334155;
}

.dark .btn-formateur-secondary:hover {
  background: #0f172a;
  border-color: #475569;
}

.dark .search-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #f1f5f9;
}

.dark .search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dark .notification-badge {
  border-color: #1e293b;
}

/* Styles de graphiques et visualisations */
.chart-container {
  border-radius: 1rem;
  overflow: hidden;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.dark .chart-container {
  background: #1e293b;
}

.chart-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Styles de navigation */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Styles de grille pour les cartes */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
}

@media (max-width: 640px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }
}
