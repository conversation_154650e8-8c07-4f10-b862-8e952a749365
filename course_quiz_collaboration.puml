@startuml Course Quiz Collaboration Diagram

' Définition des acteurs et classes
actor Utilisateur
circle CoursView
circle QuizView
circle CompetenceView
circle SousCompetenceView
circle ActionView
circle CourseController
circle QuizController
circle CompetenceController
circle SousCompetenceController
circle ActionController
circle CoursEntity
circle QuizEntity
circle CompetenceEntity
circle SousCompetenceEntity
circle ActionEntity

' Connexions entre l'utilisateur et les vues
Utilisateur -- CoursView
Utilisateur -- QuizView
Utilisateur -- CompetenceView
Utilisateur -- SousCompetenceView
Utilisateur -- ActionView

' Connexions entre les vues et les contrôleurs
CoursView -- CourseController
QuizView -- QuizController
CompetenceView -- CompetenceController
SousCompetenceView -- SousCompetenceController
ActionView -- ActionController

' Connexions entre les contrôleurs et les entités
CourseController -- CoursEntity
QuizController -- QuizEntity
CompetenceController -- CompetenceEntity
SousCompetenceController -- SousCompetenceEntity
ActionController -- ActionEntity

' Relations entre les entités
CoursEntity -- QuizEntity
QuizEntity -- CompetenceEntity
QuizEntity -- ActionEntity
CompetenceEntity -- SousCompetenceEntity

@enduml
