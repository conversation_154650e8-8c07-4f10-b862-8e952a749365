@startuml Quiz Update Sequence Design

title Diagramme de Séquence - Modification d'un quiz

actor Administrateur
participant "«View»\nQuiz.jsx" as View
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model

Administrateur -> View : Cliquer sur "Modifier" pour un quiz
View -> Controller : GET /api/quiz/{IDModule}
Controller -> Model : Récupérer le quiz
Model --> Controller : <PERSON><PERSON><PERSON> du quiz
Controller --> View : Afficher le formulaire pré-rempli

Administrateur -> View : Modifier les champs (Category, Type, MainSurface)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : PUT /api/quiz/{IDModule}
Controller -> Model : Mettre à jour le quiz
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
