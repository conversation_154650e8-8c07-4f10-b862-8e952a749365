@startuml Course Quiz Class Diagram Simple

' Définition des classes principales

  object CoursView<<View>> {
    -cours: Array
    -loading: Boolean
---
    +handleSubmit(): void
    +handleChange(): void
    +handleDelete(): void
    +handleEdit(): void
  }

  object QuizView<<view>> {
    -quizzes: Array
    -selectedQuiz: Object
    -loading: Boolean
---
    +handleSubmit(): void
    +handleImportExcel(): void
  }

  object CompetenceView<<view>> {
    -competences: Array
    -loading: Boolean
---
    +handleSubmit(): void
    +handleDelete(): void
  }

  object ActionView<<view>> {
    -actions: Array
    -loading: Boolean
---
    +handleSubmit(): void
    +handleDelete(): void
  }

  object SousCompetenceView<<view>> {
    -sousCompetences: Array
    -loading: Boolean
---
    +handleSubmit(): void
    +handleDelete(): void
  }

  object CourseController<<controller>> {
    -entityManager: EntityManagerInterface
    -coursRepository: CoursRepository
---
    +index(): JsonResponse
    +create(request: Request): JsonResponse
    +show(id: int): JsonResponse
    +update(request: Request, id: int): JsonResponse
    +delete(id: int): JsonResponse
  }

  object QuizController<<controller>> {
    -entityManager: EntityManagerInterface
    -coursRepository: CoursRepository
    -validator: ValidatorInterface
---
    +list(request: Request): JsonResponse
    +show(IDModule: string): JsonResponse
    +createBatch(request: Request): JsonResponse
    +update(request: Request, IDModule: string): JsonResponse
    +delete(id: int): JsonResponse
    +deleteByIdModule(IDModule: string): JsonResponse
  }

  object CompetenceController<<controller>> {
    -entityManager: EntityManagerInterface
    -validator: ValidatorInterface
---
    +list(request: Request): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(request: Request, id: int): JsonResponse
    +delete(id: int): JsonResponse
  }

  object ActionController<<controller>> {
    -entityManager: EntityManagerInterface
    -validator: ValidatorInterface
---
    +list(request: Request): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(request: Request, id: int): JsonResponse
    +delete(id: int): JsonResponse
  }

  object SousCompetenceController<<controller>> {
    -entityManager: EntityManagerInterface
    -validator: ValidatorInterface
---
    +list(request: Request): JsonResponse
    +show(id: int): JsonResponse
    +create(request: Request): JsonResponse
    +update(request: Request, id: int): JsonResponse
    +delete(id: int): JsonResponse
  }

  object Cours<<model>> {
    -id: int
    -titre: string
    -description: string
    -quizzes: Collection<Quiz>
  }

  object Quiz<<model>> {
    -id: int
    -IDModule: string
    -type: string
    -category: string
    -mainSurface: boolean
    -main: int
    -surface: int
    -nomFR: string
    -nomEN: string
    -cours: Cours
    -competences: Collection<Competence>
    -actions: Collection<Action>
  }

  object Competence<<model>> {
    -id: int
    -idmodule: string
    -nom_fr: string
    -nom_en: string
    -categorie_fr: string
    -categorie_en: string
    -quiz: Quiz
    -sousCompetences: Collection<SousCompetence>
  }

  object SousCompetence<<model>> {
    -id: int
    -nom_fr: string
    -nom_en: string
    -competence: Competence
  }

  object Action<<model>> {
    -id: int
    -idmodule: string
    -nom_fr: string
    -nom_en: string
    -categorie_fr: string
    -categorie_en: string
    -quiz: Quiz
  }


' Relations entre les classes avec verbes
Cours "1" *-- "0..*" Quiz : contient
Quiz "1" *-- "0..*" Competence : possède
Quiz "1" *-- "0..*" Action : inclut
Competence "1" *-- "0..*" SousCompetence : regroupe

' Relations entre les vues et les contrôleurs avec verbes
CoursView ..> CourseController : utilise
QuizView ..> QuizController : utilise >
CompetenceView ..> CompetenceController : utilise
ActionView ..> ActionController : utilise
SousCompetenceView ..> SousCompetenceController : utilise

' Relations entre les contrôleurs et les modèles avec verbes
CourseController ..> Cours : manipule
QuizController ..> Quiz : manipule
QuizController ..> Action : gère
CompetenceController ..> Competence : manipule
ActionController ..> Action : manipule
SousCompetenceController ..> SousCompetence : manipule
SousCompetenceController ..> Competence : référence

@enduml
