@startuml SousCompetence Collaboration Diagram

' Définition des acteurs et classes
actor Utilisateur
circle CompetenceView
circle SousCompetenceView
circle CompetenceController
circle SousCompetenceController
circle CompetenceEntity
circle SousCompetenceEntity
circle QuizEntity

' Connexions entre l'utilisateur et les vues
Utilisateur -- CompetenceView
Utilisateur -- SousCompetenceView

' Connexions entre les vues et les contrôleurs
CompetenceView -- CompetenceController
SousCompetenceView -- SousCompetenceController

' Connexions entre les contrôleurs et les entités
CompetenceController -- CompetenceEntity
SousCompetenceController -- SousCompetenceEntity
CompetenceController -- QuizEntity
SousCompetenceController -- CompetenceEntity

' Relations entre les entités
CompetenceEntity -- SousCompetenceEntity
QuizEntity -- CompetenceEntity

@enduml
