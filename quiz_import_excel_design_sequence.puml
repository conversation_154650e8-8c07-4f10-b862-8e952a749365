@startuml
title Diagramme de Séquence - Import d'un quiz via Excel

actor Administrateur
participant "«View»\nQuiz.jsx" as View
participant "«Library»\nXLSX" as Library
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model

Administrateur -> View : Sélectionner le fichier Excel
Administrateur -> View : Cliquer sur "Importer"

View -> View : handleExcelImport(file)
View -> Library : read(data)
Library --> View : workbook
View -> Library : utils.sheet_to_json(worksheet)
Library --> View : jsonData
View -> View : Extraire les données du quiz

opt [Gestion des compétences]
    View -> View : Ajouter un bloc de compétence
    View -> View : Supprimer un bloc de compétence
end

opt [Gestion des sous-compétences]
    View -> View : Ajouter un bloc de sous-compétence
    View -> View : Supprimer un bloc de sous-compétence
end

opt [Gestion des actions]
    View -> View : Ajouter un bloc d'action
    View -> View : Supprimer un bloc d'action
end

View -> Controller : POST /api/quiz/batch
activate Controller

Controller -> Model : Enregistrer les données
activate Model

Model --> Controller : Confirmation
deactivate Model

Controller --> View : Réponse
deactivate Controller

View --> Administrateur : Afficher le résultat

@enduml
