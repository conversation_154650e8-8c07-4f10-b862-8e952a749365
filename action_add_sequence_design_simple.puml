@startuml Action Add Sequence Design

title Diagramme de Séquence - Ajout d'une action

actor Administrateur
participant "«View»\nActionForm.jsx" as View
participant "«Controller»\nActionController" as Controller
participant "«Model»\nAction" as Model

Administrateur -> View : Remplir le formulaire (nom_fr, nom_en, categorie_fr, categorie_en)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : POST /api/action
Controller -> Model : Enregistrer l'action
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Affiche<PERSON> le résultat

@enduml
