@startuml Action Update Sequence Design

title Diagramme de Séquence - Modification d'une action

actor Administrateur
participant "«View»\nActionForm.jsx" as View
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nAction" as Model

Administrateur -> View : Cliquer sur "Modifier" pour une action
View -> View : Récupérer l'ID de l'action

Administrateur -> View : Modifier les champs (nom_fr, nom_en, categorie_fr, categorie_en)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : PUT /api/quiz/quiz-action-by-id/{id}
note right: Route définie dans QuizController.php ligne 1633
Controller -> Model : Récupérer l'action par ID
Model --> Controller : Action trouvée
Controller -> Model : Mettre à jour l'action
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : <PERSON>ff<PERSON><PERSON> le résultat

@enduml
