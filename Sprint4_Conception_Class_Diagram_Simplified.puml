@startuml Sprint4_Conception_Class_Diagram_Simplified

!theme plain
skinparam backgroundColor #FAFAFA
skinparam class {
    BackgroundColor White
    BorderColor #333333
    ArrowColor #333333
    FontSize 10
}

skinparam package {
    BackgroundColor #E8F4FD
    BorderColor #1976D2
    FontStyle bold
}

title "Sprint 4 - Diagramme de Classes de Conception Simplifié\nMessagerie et Notifications"

' ===== COUCHE VIEW (Frontend React) =====

class MessagerieView <<View>> {
    - onlineApprenants: Array
    - allConversations: Array
    - activeConversation: Object
    - activeFormateurId: int
    - messages: Object
    - newMessage: string
    - searchTerm: string
    - loading: boolean
    - error: string
}

class NotificationCenterView <<View>> {
    - notifications: Array
    - unreadCount: int
    - showNotifications: boolean
    - loading: boolean
}

class ReclamationView <<View>> {
    - reclamations: Array
    - selectedReclamation: Object
    - newReclamation: Object
    - messageLength: int
    - showConfirmDialog: boolean
    - loading: boolean
}

class EvaluationView <<View>> {
    - quiz: Object
    - competences: Array
    - actions: Array
    - checkedActions: Object
    - evaluation: string
    - alreadyEvaluated: boolean
    - satisfactoryButtonDisabled: boolean
    - nonSatisfactoryButtonDisabled: boolean
}

class CertificateView <<View>> {
    - certificateData: Object
    - saving: boolean
}

class CalendarView <<View>> {
    - currentDate: Date
    - selectedCategory: string
    - showEventForm: boolean
    - selectedEvent: Object
    - events: Array
    - upcomingEvents: Array
    - formData: Object
    - loading: boolean
    - error: string
}

' ===== COUCHE CONTROLLER (Backend Symfony) =====

class MessagerieController <<Controller>> {
    - entityManager: EntityManagerInterface
    - messagerie: MessagerieRepository
    - utilisateur: UtilisateurRepository
    - notification: NotificationRepository
}

class NotificationController <<Controller>> {
    - entityManager: EntityManagerInterface
    - notification: NotificationRepository
    - utilisateur: UtilisateurRepository
}

class ReclamationController <<Controller>> {
    - entityManager: EntityManagerInterface
    - reclamation: ReclamationRepository
    - utilisateur: UtilisateurRepository
    - notification: NotificationRepository
}

class EvaluationController <<Controller>> {
    - entityManager: EntityManagerInterface
    - evaluation: EvaluationRepository
    - quiz: QuizRepository
    - utilisateur: UtilisateurRepository
    - certificat: CertificatRepository
    - progression: ProgressionRepository
}

class CertificatController <<Controller>> {
    - entityManager: EntityManagerInterface
    - certificat: CertificatRepository
    - progression: ProgressionRepository
    - utilisateur: UtilisateurRepository
    - notification: NotificationRepository
}

class EvenementController <<Controller>> {
    - entityManager: EntityManagerInterface
    - evenement: EvenementRepository
    - utilisateur: UtilisateurRepository
    - notification: NotificationRepository
}

' ===== COUCHE MODEL (Entités Backend) =====

class Messagerie <<Model>> {
    - id: int
    - message: string
    - lu: boolean
    - date: DateTime
    - sentByFormateur: boolean
    - formateur: Utilisateur
    - apprenant: Utilisateur
    - notifications: Collection
}

class Notification <<Model>> {
    - id: int
    - Description: string
    - read: boolean
    - createdAt: DateTimeImmutable
    - type: string
    - user: Utilisateur
    - messagerie: Messagerie
    - reclamation: Reclamation
    - certificat: Certificat
    - evaluation: Evaluation
    - evenement: Evenement
}

class Reclamation <<Model>> {
    - id: int
    - subject: string
    - message: string
    - status: string
    - date: DateTime
    - response: string
    - responses: array
    - user: Utilisateur
    - notifications: Collection
}

class Evaluation <<Model>> {
    - id: int
    - statutEvaluation: string
    - idmodule: string
    - createdAt: DateTime
    - apprenantId: int
    - quiz: Quiz
    - formateur: Utilisateur
    - apprenant: Utilisateur
    - notifications: Collection
}

class Certificat <<Model>> {
    - id: int
    - dateObtention: DateTime
    - contenu: string
    - isAutoGenerated: boolean
    - apprenant: Utilisateur
    - progression: Progression
    - notifications: Collection
}

class Evenement <<Model>> {
    - id: int
    - titre: string
    - description: string
    - dateDebut: DateTime
    - dateFin: DateTime
    - journeeEntiere: boolean
    - categorie: string
    - couleur: string
    - administrateurs: Collection
    - notifications: Collection
}

class Utilisateur <<Model>> {
    - id: int
    - name: string
    - email: string
    - role: string
    - isApproved: boolean
}

' ===== RELATIONS =====

' Relations Views vers Controllers
MessagerieView --> MessagerieController : utilise
NotificationCenterView --> NotificationController : utilise
ReclamationView --> ReclamationController : utilise
EvaluationView --> EvaluationController : utilise
CertificateView --> CertificatController : utilise
CalendarView --> EvenementController : utilise

' Relations Controllers vers Models
MessagerieController --> Messagerie : manipule
MessagerieController --> Notification : manipule

NotificationController --> Notification : manipule

ReclamationController --> Reclamation : manipule
ReclamationController --> Notification : manipule

EvaluationController --> Evaluation : manipule
EvaluationController --> Certificat : manipule
EvaluationController --> Notification : manipule

CertificatController --> Certificat : manipule
CertificatController --> Progression : manipule
CertificatController --> Notification : manipule

EvenementController --> Evenement : manipule
EvenementController --> Notification : manipule

' Relations entre Models - Notification au centre
Notification "0..*" --> "0..1" Messagerie : messagerie
Notification "0..*" --> "0..1" Reclamation : reclamation
Notification "0..*" --> "0..1" Certificat : certificat
Notification "0..*" --> "0..1" Evaluation : evaluation
Notification "0..*" --> "0..1" Evenement : evenement
Notification "0..*" --> "1" Utilisateur : user

' Relations principales
Messagerie "0..*" --> "1" Utilisateur : formateur
Messagerie "0..*" --> "1" Utilisateur : apprenant

Reclamation "0..*" --> "1" Utilisateur : user

Evaluation "0..*" --> "1" Quiz : quiz
Evaluation "0..*" --> "1" Utilisateur : formateur
Evaluation "0..*" --> "1" Utilisateur : apprenant

Certificat "0..*" --> "1" Utilisateur : apprenant
Certificat "0..*" --> "1" Progression : progression

Progression "0..*" --> "1" Cours : cours
Progression "0..*" --> "1" Utilisateur : apprenant

Quiz "0..*" --> "1" Cours : cours

Evenement "0..*" --> "0..*" Utilisateur : administrateurs

@enduml
