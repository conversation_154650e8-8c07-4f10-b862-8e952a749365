@startuml Gestion des Utilisateurs
actor Administrateur
participant "Système" as <PERSON><PERSON>

ref over Administrate<PERSON>, Systeme : G<PERSON>rer les utilisateurs

== Gestion des Utilisateurs Existants ==

Administrateur -> Systeme : Accéder à la page de gestion des utilisateurs
Systeme -> Systeme : Récupérer la liste des utilisateurs approuvés
Systeme --> Administrateur : Afficher la liste des utilisateurs

alt Ajouter un utilisateur
    Administrateur -> Systeme : Cliquer sur "Ajouter un utilisateur"
    Systeme --> Administrateur : Afficher le formulaire d'ajout
    
    Administrateur -> Systeme : Remplir le formulaire
    Administrateur -> Systeme : Cliquer sur "Enregistrer"
    
    Systeme -> Systeme : Valider les données
    Systeme -> Systeme : Créer un nouvel utilisateur (statut: approuvé)
    Systeme -> Systeme : Enregistrer dans la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur ajouté avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

alt Modifier un utilisateur
    Administrateur -> Systeme : Cliquer sur l'icône "Modifier" d'un utilisateur
    Systeme -> Systeme : Récupérer les données de l'utilisateur
    Systeme --> Administrateur : Afficher le formulaire de modification
    
    Administrateur -> Systeme : Modifier les informations
    Administrateur -> Systeme : Cliquer sur "Enregistrer"
    
    Systeme -> Systeme : Valider les données
    Systeme -> Systeme : Mettre à jour l'utilisateur dans la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur modifié avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

alt Supprimer un utilisateur
    Administrateur -> Systeme : Cliquer sur l'icône "Supprimer" d'un utilisateur
    Systeme --> Administrateur : Afficher une boîte de dialogue de confirmation
    
    Administrateur -> Systeme : Confirmer la suppression
    
    Systeme -> Systeme : Supprimer l'utilisateur de la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur supprimé avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

alt Assigner un rôle
    Administrateur -> Systeme : Cliquer sur le menu déroulant de rôle d'un utilisateur
    Systeme --> Administrateur : Afficher les options de rôle
    
    Administrateur -> Systeme : Sélectionner un nouveau rôle
    
    Systeme -> Systeme : Mettre à jour le rôle dans la base de données
    
    Systeme --> Administrateur : Afficher "Rôle mis à jour avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

== Gestion des Demandes d'Inscription ==

Administrateur -> Systeme : Accéder à la page des demandes d'inscription
Systeme -> Systeme : Récupérer la liste des utilisateurs en attente d'approbation
Systeme --> Administrateur : Afficher la liste des demandes

alt Approuver un utilisateur
    Administrateur -> Systeme : Cliquer sur "Approuver" pour un utilisateur
    Systeme --> Administrateur : Afficher une boîte de dialogue de confirmation
    
    Administrateur -> Systeme : Confirmer l'approbation
    
    Systeme -> Systeme : Marquer l'utilisateur comme approuvé dans la base de données
    Systeme -> Systeme : Envoyer un email de confirmation à l'utilisateur
    
    Systeme --> Administrateur : Afficher "Utilisateur approuvé avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des demandes
end

alt Rejeter un utilisateur
    Administrateur -> Systeme : Cliquer sur "Rejeter" pour un utilisateur
    Systeme --> Administrateur : Afficher une boîte de dialogue avec champ de raison
    
    Administrateur -> Systeme : Saisir la raison du rejet
    Administrateur -> Systeme : Confirmer le rejet
    
    Systeme -> Systeme : Envoyer un email de rejet à l'utilisateur avec la raison
    Systeme -> Systeme : Supprimer l'utilisateur de la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur rejeté avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des demandes
end

@enduml
