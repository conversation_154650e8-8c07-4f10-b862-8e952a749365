@startuml Diagramme de Séquence - Inscription (MVC)

actor Utilisateur
participant "<<View>>\nRegister.jsx" as View
participant "<<Controller>>\nAuthContext" as Controller
participant "<<Controller>>\nAuthController" as BackendController
participant "<<Model>>\nUtilisateur" as Model

title Diagramme de Séquence - Inscription (Architecture MVC)

== Affichage du formulaire ==

Utilisateur -> View : Accéder à la page d'inscription
activate View
View --> Utilisateur : Afficher le formulaire d'inscription
deactivate View

== Remplissage du formulaire ==

Utilisateur -> View : Remplir le formulaire (nom, email, mot de passe, téléphone, rôle)
activate View
View -> View : Mettre à jour les états des champs
View --> Utilisateur : Afficher les données saisies
deactivate View

Utilisateur -> View : Télécharger une image de profil (optionnel)
activate View
View -> View : Traiter le fichier image
View --> Utilisateur : Afficher l'aperçu de l'image
deactivate View

== Soumission du formulaire ==

Utilisateur -> View : Cliquer sur "S'inscrire"
activate View

View -> View : Valider les données du formulaire
View -> Controller : register(userData)
activate Controller

Controller -> BackendController : POST /api/register
activate BackendController

BackendController -> BackendController : Valider les données
BackendController -> Model : create(userData)
activate Model
Model --> BackendController : utilisateur
deactivate Model

BackendController -> BackendController : persist(utilisateur)
BackendController --> Controller : response
deactivate BackendController

alt errors.length==0
    Controller --> View : {success: true, user: data}
    View --> Utilisateur : afficher "Inscription réussie. Votre compte est en attente d'approbation."
else
    Controller --> View : {success: false, error: messages}
    View --> Utilisateur : afficher les erreurs
end

deactivate Controller
deactivate View

@enduml
