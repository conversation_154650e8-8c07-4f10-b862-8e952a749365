@startuml Quiz Import Excel Sequence Design

title Diagramme de Séquence - Import d'un quiz via Excel

actor Utilisateur
participant "«View»\nQuizManagementPage.jsx" as View
participant "«Library»\nXLSX" as XLSX
participant "«Controller»\nQuizController" as Controller
participant "«Model»\nQuiz" as Model

ref over Utilisateur, Model
  Importer un quiz via Excel
end ref

Utilisateur -> View : Cliquer sur "Importer un quiz via Excel"
View --> Utilisateur : Afficher le formulaire d'import Excel

Utilisateur -> View : Sélectionner le fichier Excel
Utilisateur -> View : Cliquer sur "Importer"
View -> View : handleExcelImport(file)

View -> XLSX : read(data)
XLSX --> View : workbook

View -> XLSX : utils.sheet_to_json(worksheet)
XLSX --> View : jsonData

View -> View : Extraire et formater les données\n(quiz, compétences, sous-compétences, actions)

View -> Controller : POST /api/quiz/batch
Controller -> Controller : Valider les données
Controller -> Model : beginTransaction()
Controller -> Model : createQuiz(quizData)
Controller -> Model : createCompetences(competencesData)
Controller -> Model : createSousCompetences(sousCompetencesData)
Controller -> Model : createActions(actionsData)
Controller -> Model : commitTransaction()
Model --> Controller : response

alt [success: true]
    Controller --> View : {success: true, quiz: data}
    View -> View : Mettre à jour la liste des quiz
    View --> Utilisateur : Afficher "Quiz importé avec succès"
else [success: false]
    Controller --> View : {success: false, error: messages}
    View --> Utilisateur : Afficher les erreurs d'importation
end

@enduml
