@startuml Sprint 3 - Diagramme 5 : Création d'Événements d'Évaluation

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Administrateur" as Admin
actor "Apprenant" as A
actor "Formateur" as F
participant "Système" as S

== Création d'Événements d'Évaluation ==

group Création d'événement d'évaluation
    Admin -> S : Accéder au calendrier des événements
    S --> Admin : Afficher le calendrier
    Admin -> S : Créer un nouvel événement
    S --> Admin : Afficher le formulaire de création
    Admin -> S : Saisir les détails de l'événement
    Admin -> S : Valider la création
    S -> S : Enregistrer l'événement
    S -> A : Envoyer notification aux apprenants
    S -> F : Envoyer notification aux formateurs
    S --> Admin : Confirmer la création de l'événement
end

@enduml
