@startuml Course Delete Sequence Design

title Diagramme de Séquence - Suppression d'un cours

actor Administrateur
participant "«View»\nCours.jsx" as View
participant "«Controller»\nCourseController" as Controller
participant "«Model»\nCours" as Model

Administrateur -> View : Cliquer sur "Supprimer" pour un cours
View --> Administrateur : Demander confirmation

Administrateur -> View : Confirmer la suppression
View -> Controller : DELETE /api/cours/{id}
Controller -> Model : Supprimer le cours
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
