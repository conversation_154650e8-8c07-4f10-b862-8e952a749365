@startuml Sprint 3 - Diagramme 1B : Attribution d'Évaluations par le Formateur - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F

participant "«View»\nQuizDetailsView" as QDV

participant "«Controller»\nEvaluationController" as EC
participant "«Controller»\nCertificatController" as CC

participant "«Model»\nEvaluation" as EM
participant "«Model»\nProgression" as PM

== Attribution d'un statut "Satisfaisant" ou "Non Satisfaisant" ==

F -> QDV : Évaluer les compétences du quiz
QDV -> QDV : Vérifier que toutes les actions sont cochées
QDV -> QDV : Activer bouton "Satisfaisant" si toutes actions validées

note right of QDV : Le bouton "Satisfaisant" n'est\nactivé que si toutes les actions\nsont cochées par le formateur

F -> QDV : Cliquer sur "Satisfaisant" ou "Non Satisfaisant"
QDV -> EC : create(Request $request)

EC -> EC : json_decode($request->getContent(), true)
EC -> EC : Validation des données requises ['quizId', 'apprenantId', 'statut']

alt Données manquantes
    EC --> QDV : JsonResponse error "Missing required fields"
else Statut invalide (pas 'Satisfaisant' ou 'Non Satisfaisant')
    EC --> QDV : JsonResponse error "Invalid status"
else Quiz non trouvé
    EC --> QDV : JsonResponse error "Quiz not found"
else Apprenant non trouvé
    EC --> QDV : JsonResponse error "Apprenant not found"
else Utilisateur n'est pas formateur
    EC --> QDV : JsonResponse error "User must be a formateur"
else Données valides
    alt Évaluation existe déjà
        EC -> EM : findOneBy(['quiz' => $quiz, 'formateur' => $user, 'apprenant' => $apprenant])
        EM --> EC : $existingEvaluation
        EC -> EM : setStatutEvaluation($data['statut'])
        EM -> EM : synchronizeIdmodule()
        EC -> EC : flush()
        EC --> QDV : JsonResponse "Evaluation updated successfully"
    else Nouvelle évaluation
        EC -> EM : new Evaluation()
        EM -> EM : setStatutEvaluation($data['statut'])
        EM -> EM : setQuiz($quiz)
        EM -> EM : setFormateur($user)
        EM -> EM : setApprenant($apprenant)
        EM -> EM : synchronizeIdmodule()
        EC -> EC : persist($evaluation)

        EC -> PM : findOneBy(['cours' => $cours, 'evaluation' => $evaluation, 'apprenant' => $apprenant])

        alt Progression existe
            PM -> PM : setTableEvaluations($tableEvaluations + nouvelle évaluation)
        else Nouvelle progression
            EC -> PM : new Progression()
            PM -> PM : setCours($cours)
            PM -> PM : setEvaluation($evaluation)
            PM -> PM : setApprenant($apprenant)
            PM -> PM : setTableEvaluations(['quiz_id' => $quiz->getId(), 'statut' => $data['statut']])
            EC -> EC : persist($progression)
        end

        EC -> EC : flush()
    end
end

alt $data['statut'] === "Satisfaisant"
    EC -> EC : checkAndGenerateCertificateIfNeeded($apprenant, $cours)

    alt Certificat existe déjà pour ce cours
        EC --> QDV : response['certificate'] = ['certificat_exists' => true, 'certificat' => [...]]
        note right of EC : Retourne les informations\ndu certificat existant\navec ID et date d'obtention
    else Tous les quiz du cours sont "Satisfaisant" (successRate = 100.0)
        EC -> PM : findOneBy(['cours' => $cours, 'apprenant' => $apprenant])

        alt Progression existe
            PM -> PM : setTableEvaluations(['success_rate' => 100, 'quizzes_total' => $totalQuizzes])
            EC -> EC : flush()
        else Nouvelle progression
            EC -> PM : new Progression()
            PM -> PM : setCours($cours)
            PM -> PM : setApprenant($apprenant)
            PM -> PM : setTableEvaluations(['success_rate' => 100])
            EC -> EC : persist($progression)
            EC -> EC : flush()
        end

        EC --> QDV : response['certificate'] = ['course_completed' => true, 'message' => "Utilisez le bouton..."]

        note right of EC : Génération automatique désactivée\nLe formateur doit utiliser\nle bouton de génération manuelle
    else successRate < 100.0
        EC --> QDV : certificateInfo = null

        note right of EC : L'apprenant doit compléter\ntous les quiz du cours\navec statut "Satisfaisant"
    end
else $data['statut'] === "Non Satisfaisant"
    EC --> QDV : certificateInfo = null (pas de vérification)

    note right of EC : Aucune vérification de certificat\npour les évaluations\n"Non Satisfaisant"
end

EC --> QDV : JsonResponse avec evaluation et certificate (si applicable)
QDV --> F : afficher confirmation d'évaluation

@enduml
