@startuml Sprint 3 - Diagramme 1A : Sélection de Cours par le Formateur - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F

participant "«View»\nQuizDetailsView" as QDV
participant "«View»\nApprenantCoursView" as ACV

participant "«Controller»\nProgressionController" as PC
participant "«Controller»\nEvaluationController" as EC

participant "«Model»\nApprenant" as AM
participant "«Model»\nProgression" as PM
participant "«Model»\nQuiz" as QM
participant "«Model»\nEvaluation" as EM

== Sélection d'un cours d'un apprenant par le formateur ==

F -> QDV : Cliquer sur "Sélectionner un apprenant"
QDV -> ACV : Naviguer vers ApprenantsList
ACV -> PC : getProgressionByApprenant(apprenantId)
PC -> AM : find(apprenantId)
AM --> PC : apprenant
PC -> PM : findBy(['apprenant' => apprenant])
PM --> PC : progressions[]
PC --> ACV : progressionData avec cours
ACV --> F : afficher cours disponibles

note right of ACV : Liste des cours de l'apprenant\navec pourcentages de progression

F -> ACV : Sélectionner un cours spécifique
ACV -> QDV : Naviguer vers QuizDetails
QDV -> EC : getEvaluationByQuizAndApprenant(quizId, apprenantId)
EC -> QM : find(quizId)
QM --> EC : quiz
EC -> AM : find(apprenantId)
AM --> EC : apprenant
EC -> EM : findOneBy(['quiz' => quiz, 'apprenant' => apprenant])
EM --> EC : evaluation ou null
EC --> QDV : quiz details et évaluation existante
QDV --> F : afficher interface d'évaluation

note right of QDV : Interface prête pour\nl'évaluation des compétences

@enduml
