@startuml Sprint 3 - Diagramme 1B : Évaluation des Actions et Gestion MainSurface

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F
participant "Système" as S

== Évaluation des Actions et Gestion MainSurface ==

group Évaluation des actions
    F -> S : Consulter les actions du quiz
    S --> F : Afficher les actions avec cases à cocher

    loop Pour chaque action
        F -> S : Cocher/décocher l'action selon sa réalisation
        S --> F : Mettre à jour l'état de l'action en temps réel
        S -> S : Sauvegarder l'état de l'action
    end

    note right of S : Toutes les actions doivent être cochées\npour pouvoir attribuer "Satisfaisant"
end

group Gestion des champs spéciaux

    alt Quiz avec MainSurface = 1
        F -> S : Saisir les valeurs Main et Surface
        S --> F : Afficher les champs éditables
        F -> S : Valider les valeurs saisies
        S -> S : Enregistrer les valeurs Main et Surface

        note right of S : Champs Main et Surface\nsont éditables et obligatoires

    else Quiz avec MainSurface = 0
        S --> F : Afficher l'interface sans champs Main/Surface

        note right of S : Les champs Main et Surface\nne sont pas affichés
    end
end

@enduml
