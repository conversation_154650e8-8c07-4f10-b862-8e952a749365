<?php //resource_name_collection

return [PHP_INT_MAX, static fn () => \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['ApiPlatform\\Metadata\\Resource\\ResourceNameCollection'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('ApiPlatform\\Metadata\\Resource\\ResourceNameCollection')),
    ],
    null,
    [
        'ApiPlatform\\Metadata\\Resource\\ResourceNameCollection' => [
            'classes' => [
                [
                    'ApiPlatform\\State\\ApiResource\\Error',
                    'ApiPlatform\\Symfony\\Validator\\Exception\\ValidationException',
                    'ApiPlatform\\Validator\\Exception\\ValidationException',
                ],
            ],
        ],
    ],
    $o[0],
    []
)];
