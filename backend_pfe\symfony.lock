{"api-platform/core": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "74b45ac570c57eb1fbe56c984091a9ff87e18bab"}, "files": ["./config/packages/api_platform.yaml", "./config/routes/api_platform.yaml", "./src/ApiResource/.gitignore"]}, "doctrine/doctrine-bundle": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.10", "ref": "d1778a69711a9b06bb4e202977ca6c4a0d16933d"}, "files": ["./config/packages/doctrine.yaml", "./src/Entity/.gitignore", "./src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["./config/packages/doctrine_migrations.yaml", "./migrations/.gitignore"]}, "nelmio/cors-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["./config/packages/nelmio_cors.yaml"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "6a9341aa97d441627f8bd424ae85dc04c944f8b4"}, "files": ["./.env.test", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/asset-mapper": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "5ad1308aa756d58f999ffbe1540d1189f5d7d14a"}, "files": ["./assets/app.js", "./assets/styles/app.css", "./config/packages/asset_mapper.yaml", "./importmap.php"]}, "symfony/console": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["./bin/console"]}, "symfony/debug-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["./config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": ["./.env", "./.env.dev"]}, "symfony/framework-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "32126346f25e1cee607cc4aa6783d46034920554"}, "files": ["./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/preload.php", "./config/routes/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/google-mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "f8fd4ddb9b477510f8f4bce2b9c054ab428c0120"}}, "symfony/mailer": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["./config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.62", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["./config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["./config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["./config/packages/notifier.yaml"]}, "symfony/phpunit-bridge": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": ["./.env.test", "./bin/phpunit", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/routing": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["./config/packages/security.yaml", "./config/routes/security.yaml"]}, "symfony/stimulus-bundle": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "6acd9ff4f7fd5626d2962109bd4ebab351d43c43"}, "files": ["./assets/bootstrap.js", "./assets/controllers.json", "./assets/controllers/hello_controller.js"]}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["./config/packages/translation.yaml", "./translations/.gitignore"]}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["./config/packages/twig.yaml", "./templates/base.html.twig"]}, "symfony/ux-turbo": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.19", "ref": "9dd2778a116b6e5e01e5e1582d03d5a9e82630de"}}, "symfony/validator": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["./config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "8b51135b84f4266e3b4c8a6dc23c9d1e32e543b7"}, "files": ["./config/packages/web_profiler.yaml", "./config/routes/web_profiler.yaml"]}, "twig/extra-bundle": {"version": "v3.20.0"}}