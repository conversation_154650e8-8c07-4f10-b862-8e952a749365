{"name": "template-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@tailwindcss/vite": "^4.0.15", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.8.4", "clsx": "^2.1.1", "cors": "^2.8.5", "csx": "^10.0.2", "emoji-mart": "^5.6.0", "framer-motion": "^12.6.3", "html2pdf.js": "^0.10.3", "lucide-react": "^0.483.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-feather": "^2.0.10", "react-icons": "^5.5.0", "react-router-dom": "^7.4.0", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "sheetjs": "^2.0.0", "tailwind-merge": "^3.0.2", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "cors-anywhere": "^0.4.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "http-proxy-middleware": "^3.0.5", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "vite": "^6.2.0"}}