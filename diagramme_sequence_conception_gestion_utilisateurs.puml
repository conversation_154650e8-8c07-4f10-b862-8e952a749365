@startuml Diagramme de Séquence de Conception - Sprint 1 (Gestion des Utilisateurs)

actor Administrateur
participant "<<View>>\nUsersManagementPage" as UsersView
participant "<<View>>\nRequestsPage" as RequestsView
participant "<<ViewModel>>\nAuthContext" as AuthContext
participant "<<Model>>\nAdminController" as AdminController
participant "<<Model>>\nUtilisateur" as UtilisateurModel
participant "<<Model>>\nEmailService" as EmailService

title Diagramme de Séquence de Conception - Sprint 1 (Gestion des Utilisateurs)

ref over Administrateur, EmailService : Gérer les utilisateurs

== Afficher les utilisateurs ==

Administrateur -> UsersView : Cliquer sur "Gestion des utilisateurs"
activate UsersView

UsersView -> AuthContext : getApprovedUsers()
activate AuthContext

AuthContext -> AdminController : GET /api/admin/users
activate AdminController

AdminController -> UtilisateurModel : findApprovedUsers()
activate UtilisateurModel
UtilisateurModel --> AdminController : users
deactivate UtilisateurModel

AdminController --> AuthContext : response
deactivate AdminController

AuthContext --> UsersView : {success: true, users: data}
deactivate AuthContext

UsersView --> Administrateur : afficher liste des utilisateurs

== Ajouter un utilisateur ==

Administrateur -> UsersView : Cliquer sur "Ajouter un utilisateur"
UsersView --> Administrateur : afficher formulaire d'ajout

Administrateur -> UsersView : Remplir le formulaire et soumettre
activate UsersView

UsersView -> AuthContext : addUser(userData)
activate AuthContext

AuthContext -> AdminController : POST /api/admin/users
activate AdminController

AdminController -> UtilisateurModel : create(userData)
activate UtilisateurModel
UtilisateurModel --> AdminController : user
deactivate UtilisateurModel

AdminController -> AdminController : validate(user)
AdminController -> AdminController : persist(user)
AdminController --> AuthContext : response
deactivate AdminController

alt errors.length==0
    AuthContext --> UsersView : {success: true, user: data}
    UsersView --> Administrateur : afficher "Utilisateur ajouté avec succès"
else
    AuthContext --> UsersView : {success: false, error: messages}
    UsersView --> Administrateur : afficher les erreurs
end

deactivate AuthContext
deactivate UsersView

== Gérer les demandes d'inscription ==

Administrateur -> RequestsView : Cliquer sur "Demandes d'inscription"
activate RequestsView

RequestsView -> AuthContext : getPendingUsers()
activate AuthContext

AuthContext -> AdminController : GET /api/admin/users/pending
activate AdminController

AdminController -> UtilisateurModel : findPendingUsers()
activate UtilisateurModel
UtilisateurModel --> AdminController : pendingUsers
deactivate UtilisateurModel

AdminController --> AuthContext : response
deactivate AdminController

AuthContext --> RequestsView : {success: true, users: data}
deactivate AuthContext

RequestsView --> Administrateur : afficher liste des demandes

== Approuver un utilisateur ==

Administrateur -> RequestsView : Cliquer sur "Approuver"
RequestsView --> Administrateur : afficher boîte de dialogue de confirmation
Administrateur -> RequestsView : Confirmer l'approbation
activate RequestsView

RequestsView -> AuthContext : approveUser(userId)
activate AuthContext

AuthContext -> AdminController : POST /api/admin/users/approve/{id}
activate AdminController

AdminController -> UtilisateurModel : find(userId)
activate UtilisateurModel
UtilisateurModel --> AdminController : user
deactivate UtilisateurModel

AdminController -> UtilisateurModel : setApproved(true)
activate UtilisateurModel
UtilisateurModel --> AdminController : user
deactivate UtilisateurModel

AdminController -> EmailService : sendApprovalEmail(user)
activate EmailService
EmailService --> AdminController : emailSent
deactivate EmailService

AdminController --> AuthContext : response
deactivate AdminController

alt errors.length==0
    AuthContext --> RequestsView : {success: true}
    RequestsView --> Administrateur : afficher "Utilisateur approuvé avec succès"
else
    AuthContext --> RequestsView : {success: false, error: message}
    RequestsView --> Administrateur : afficher les erreurs
end

deactivate AuthContext
deactivate RequestsView

== Rejeter un utilisateur ==

Administrateur -> RequestsView : Cliquer sur "Rejeter"
RequestsView --> Administrateur : afficher boîte de dialogue avec champ de raison
Administrateur -> RequestsView : Saisir la raison et confirmer
activate RequestsView

RequestsView -> AuthContext : rejectUser(userId, reason)
activate AuthContext

AuthContext -> AdminController : POST /api/admin/users/reject/{id}
activate AdminController

AdminController -> UtilisateurModel : find(userId)
activate UtilisateurModel
UtilisateurModel --> AdminController : user
deactivate UtilisateurModel

AdminController -> EmailService : sendRejectionEmail(user, reason)
activate EmailService
EmailService --> AdminController : emailSent
deactivate EmailService

AdminController -> UtilisateurModel : delete(user)
activate UtilisateurModel
UtilisateurModel --> AdminController : deleted
deactivate UtilisateurModel

AdminController --> AuthContext : response
deactivate AdminController

alt errors.length==0
    AuthContext --> RequestsView : {success: true}
    RequestsView --> Administrateur : afficher "Utilisateur rejeté avec succès"
else
    AuthContext --> RequestsView : {success: false, error: message}
    RequestsView --> Administrateur : afficher les erreurs
end

deactivate AuthContext
deactivate RequestsView

@enduml
