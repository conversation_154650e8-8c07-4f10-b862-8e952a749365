@startuml Diagramme de Séquence - Visualisation Progression et Téléchargement Certificats

participant "Apprenant" as A
participant "«View»\nDashboard.jsx" as V

participant "«Controller»\nCertificatController" as CC
participant "«Controller»\nProgressionController" as PC
participant "«Model»\nCours" as MC
participant "«Model»\nProgression" as MP
participant "«Model»\nCertificat" as MCE

== Visualisation de la progression ==

A -> V : Accéder au tableau de bord
activate V

note over V : L'apprenant accède directement à ses cours\nvia ApprenantController::getMesCours()

V -> PC : getProgressionByApprenant(apprenantId)
activate PC
PC -> MP : findByApprenant(apprenantId)
activate MP
MP --> PC : progressions
deactivate MP
PC --> V : JsonResponse(progressionData)
deactivate PC

alt Certificat disponible (progression = 100%)
    V -> CC : getCertificatsByApprenant(apprenantId)
    activate CC
    CC -> MCE : findBy(['apprenant' => apprenantId])
    activate MCE
    MCE --> CC : certificats
    deactivate MCE
    CC --> V : JsonResponse(certificats)
    deactivate CC
    V --> A : afficher le bouton de téléchargement du certificat

else Pas de certificat disponible
    V --> A : afficher la progression sans bouton certificat
end

deactivate V

== Téléchargement de certificat ==

A -> V : Cliquer sur "Télécharger le certificat"
activate V
V -> CC : download(certificatId)
activate CC
CC -> MCE : findById(certificatId)
activate MCE

alt Certificat existe
    MCE --> CC : certificat
    deactivate MCE
    CC -> CC : generatePDF(certificat)
    CC --> V : Response(pdfFile)
    deactivate CC
    V --> A : télécharger le fichier PDF

else Certificat inexistant
    MCE --> CC : null
    deactivate MCE
    CC --> V : JsonResponse("Certificat non disponible")
    deactivate CC
    V --> A : afficher "Certificat non disponible"
end

deactivate V

@enduml
