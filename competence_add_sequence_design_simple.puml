@startuml Competence Add Sequence Design

title Diagramme de Séquence - Ajout d'une compétence

actor Administrateur
participant "«View»\nCompetenceForm.jsx" as View
participant "«Controller»\nCompetenceController" as Controller
participant "«Model»\nCompetence" as Model

Administrateur -> View : Remplir le formulaire (nom_fr, nom_en, categorie_fr, categorie_en)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : POST /api/competence
Controller -> Model : Enregistrer la compétence
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
