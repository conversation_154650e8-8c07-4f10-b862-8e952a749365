# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
  app.mailer_sender: "<EMAIL>"
  # Les paramètres du chatbot ont été supprimés

services:
  # default configuration for services in *this* file
  _defaults:
    autowire: true # Automatically injects dependencies in your services.
    autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

  # makes classes in src/ available to be used as services
  # this creates a service per class whose id is the fully-qualified class name
  App\:
    resource: "../src/"
    exclude:
      - "../src/DependencyInjection/"
      - "../src/Entity/"
      - "../src/Kernel.php"

  # Le service ChatbotService a été supprimé

  # add more service definitions when explicit configuration is needed
  # please note that last definitions always *replace* previous ones

  # CORS Listener (désactivé car nous utilisons nelmio/cors-bundle)
  # App\EventListener\CorsListener:
  #   tags:
  #     - { name: kernel.event_subscriber }

  # Service WebSocket pour les notifications en temps réel
  App\WebSocket\NotificationServer:
    arguments:
      $entityManager: "@doctrine.orm.entity_manager"
      $utilisateurRepository: '@App\Repository\UtilisateurRepository'
      $logger: "@logger"

  # Service pour envoyer des notifications via WebSocket
  App\Service\WebSocketNotificationService:
    arguments:
      $logger: "@logger"
      $container: "@service_container"

  # Service pour envoyer des emails
  App\Service\EmailService:
    arguments:
      $senderEmail: "%app.mailer_sender%"
