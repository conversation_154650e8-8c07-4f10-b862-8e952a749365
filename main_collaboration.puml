@startuml Main Collaboration Diagram

' Définition des acteurs principaux
actor Administrateur
actor Formateur
actor Apprenant

' Définition des principales vues et contrôleurs
circle AuthController
circle CourseController
circle QuizController
circle EvaluationController
circle CertificateController
circle ChatbotController

' Définition des principales entités
circle UtilisateurEntity
circle CoursEntity
circle QuizEntity
circle EvaluationEntity
circle CertificatEntity
circle ChatbotConversationEntity

' Connexions entre les acteurs et les contrôleurs
Administrateur -- AuthController
Administrateur -- CourseController
Administrateur -- QuizController

Formateur -- AuthController
Formateur -- CourseController
Formateur -- EvaluationController

Apprenant -- AuthController
Apprenant -- CourseController
Apprenant -- CertificateController
Apprenant -- ChatbotController

' Connexions entre les contrôleurs et les entités
AuthController -- UtilisateurEntity
CourseController -- CoursEntity
QuizController -- QuizEntity
EvaluationController -- EvaluationEntity
CertificateController -- CertificatEntity
ChatbotController -- ChatbotConversationEntity

' Relations entre les entités principales
UtilisateurEntity -- CoursEntity
CoursEntity -- QuizEntity
CoursEntity -- EvaluationEntity
CoursEntity -- CertificatEntity
UtilisateurEntity -- ChatbotConversationEntity

@enduml
