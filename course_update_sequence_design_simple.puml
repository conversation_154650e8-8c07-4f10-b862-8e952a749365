@startuml Course Update Sequence Design

title Diagramme de Séquence - Modification d'un cours

actor Administrateur
participant "«View»\nCours.jsx" as View
participant "«Controller»\nCourseController" as Controller
participant "«Model»\nCours" as Model

Administrateur -> View : Cliquer sur "Modifier" pour un cours
View -> Controller : GET /api/cours/{id}
Controller -> Model : R<PERSON><PERSON><PERSON>rer le cours
Model --> Controller : Donn<PERSON> du cours
Controller --> View : Afficher le formulaire pré-rempli

Administrateur -> View : Modifier les champs (titre, description)
Administrateur -> View : Cliquer sur "Enregistrer"
View -> Controller : PUT /api/cours/{id}
Controller -> Model : Mettre à jour le cours
Model --> Controller : Confirmation
Controller --> View : Réponse
View --> Administrateur : Afficher le résultat

@enduml
