@startuml Inscription Utilisateur
actor Utilisateur
participant "Système" as Systeme

ref over Utilisateur, Systeme : S'inscrire sur la plateforme

Utilisateur -> Systeme : Accéder à la page d'inscription
Systeme --> Utilisateur : Afficher le formulaire d'inscription

Utilisateur -> Systeme : Remplir le formulaire (nom, email, mot de passe, téléphone, rôle)
Utilisateur -> Systeme : Télécharger une image de profil (optionnel)
Utilisateur -> Systeme : Cliquer sur "S'inscrire"

Systeme -> Systeme : Valider les données du formulaire

alt Données invalides
    Systeme --> Utilisateur : Afficher les erreurs de validation
else Données valides
    Systeme -> Systeme : Créer un compte utilisateur
    Systeme -> Systeme : Enregistrer les informations dans la base de données
    
    alt Approbation automatique
        Systeme --> Utilisateur : Afficher "Inscription réussie. Vous pouvez vous connecter immédiatement."
    else Approbation requise
        Systeme --> Utilisateur : Afficher "Inscription réussie. Votre compte est en attente d'approbation par un administrateur."
    end
end

@enduml

@startuml Connexion Utilisateur
actor Utilisateur
participant "Système" as Systeme

ref over Utilisateur, Systeme : Se connecter à la plateforme

Utilisateur -> Systeme : Accéder à la page de connexion
Systeme --> Utilisateur : Afficher le formulaire de connexion

Utilisateur -> Systeme : Saisir email et mot de passe
Utilisateur -> Systeme : Cliquer sur "Se connecter"

Systeme -> Systeme : Vérifier les identifiants

alt Identifiants invalides
    Systeme --> Utilisateur : Afficher "Identifiants invalides"
else Identifiants valides
    alt Compte non approuvé
        Systeme --> Utilisateur : Afficher "Votre compte est en attente d'approbation"
    else Compte approuvé
        Systeme -> Systeme : Générer un token JWT
        Systeme -> Systeme : Enregistrer la session utilisateur
        
        alt Rôle = Administrateur
            Systeme --> Utilisateur : Rediriger vers le tableau de bord administrateur
        else Rôle = Formateur
            Systeme --> Utilisateur : Rediriger vers le tableau de bord formateur
        else Rôle = Apprenant
            Systeme --> Utilisateur : Rediriger vers le tableau de bord apprenant
        end
    end
end

@enduml

@startuml Déconnexion Utilisateur
actor Utilisateur
participant "Système" as Systeme

ref over Utilisateur, Systeme : Se déconnecter de la plateforme

Utilisateur -> Systeme : Cliquer sur "Déconnexion"
Systeme -> Systeme : Supprimer le token JWT
Systeme -> Systeme : Terminer la session utilisateur
Systeme --> Utilisateur : Rediriger vers la page d'accueil

@enduml

@startuml Réinitialisation Mot de Passe
actor Utilisateur
participant "Système" as Systeme

ref over Utilisateur, Systeme : Réinitialiser le mot de passe

Utilisateur -> Systeme : Accéder à la page "Mot de passe oublié"
Systeme --> Utilisateur : Afficher le formulaire de demande de réinitialisation

Utilisateur -> Systeme : Saisir l'email
Utilisateur -> Systeme : Cliquer sur "Réinitialiser mon mot de passe"

Systeme -> Systeme : Vérifier l'existence de l'email

alt Email existe
    Systeme -> Systeme : Générer un token de réinitialisation
    Systeme -> Systeme : Enregistrer le token dans la base de données
    Systeme -> Systeme : Envoyer un email avec le lien de réinitialisation
end

Systeme --> Utilisateur : Afficher "Si votre email est associé à un compte, vous recevrez un lien de réinitialisation"

... Utilisateur reçoit l'email et clique sur le lien ...

Utilisateur -> Systeme : Accéder à la page de réinitialisation avec le token
Systeme -> Systeme : Vérifier la validité du token

alt Token invalide ou expiré
    Systeme --> Utilisateur : Afficher "Token invalide ou expiré"
else Token valide
    Systeme --> Utilisateur : Afficher le formulaire de nouveau mot de passe
    
    Utilisateur -> Systeme : Saisir le nouveau mot de passe
    Utilisateur -> Systeme : Cliquer sur "Enregistrer"
    
    Systeme -> Systeme : Mettre à jour le mot de passe dans la base de données
    Systeme -> Systeme : Supprimer le token de réinitialisation
    
    Systeme --> Utilisateur : Afficher "Mot de passe réinitialisé avec succès"
    Systeme --> Utilisateur : Rediriger vers la page de connexion
end

@enduml

@startuml Gestion Utilisateurs (Administrateur)
actor Administrateur
participant "Système" as Systeme

ref over Administrateur, Systeme : Gérer les utilisateurs

Administrateur -> Systeme : Accéder à la page de gestion des utilisateurs
Systeme -> Systeme : Récupérer la liste des utilisateurs approuvés
Systeme --> Administrateur : Afficher la liste des utilisateurs

alt Ajouter un utilisateur
    Administrateur -> Systeme : Cliquer sur "Ajouter un utilisateur"
    Systeme --> Administrateur : Afficher le formulaire d'ajout
    
    Administrateur -> Systeme : Remplir le formulaire
    Administrateur -> Systeme : Cliquer sur "Enregistrer"
    
    Systeme -> Systeme : Valider les données
    Systeme -> Systeme : Créer un nouvel utilisateur
    Systeme -> Systeme : Enregistrer dans la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur ajouté avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

alt Modifier un utilisateur
    Administrateur -> Systeme : Cliquer sur l'icône "Modifier" d'un utilisateur
    Systeme -> Systeme : Récupérer les données de l'utilisateur
    Systeme --> Administrateur : Afficher le formulaire de modification
    
    Administrateur -> Systeme : Modifier les informations
    Administrateur -> Systeme : Cliquer sur "Enregistrer"
    
    Systeme -> Systeme : Valider les données
    Systeme -> Systeme : Mettre à jour l'utilisateur dans la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur modifié avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

alt Supprimer un utilisateur
    Administrateur -> Systeme : Cliquer sur l'icône "Supprimer" d'un utilisateur
    Systeme --> Administrateur : Afficher une boîte de dialogue de confirmation
    
    Administrateur -> Systeme : Confirmer la suppression
    
    Systeme -> Systeme : Supprimer l'utilisateur de la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur supprimé avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

alt Assigner un rôle
    Administrateur -> Systeme : Cliquer sur le menu déroulant de rôle d'un utilisateur
    Systeme --> Administrateur : Afficher les options de rôle
    
    Administrateur -> Systeme : Sélectionner un nouveau rôle
    
    Systeme -> Systeme : Mettre à jour le rôle dans la base de données
    
    Systeme --> Administrateur : Afficher "Rôle mis à jour avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des utilisateurs
end

@enduml

@startuml Approbation Utilisateurs (Administrateur)
actor Administrateur
participant "Système" as Systeme

ref over Administrateur, Systeme : Approuver/Rejeter les demandes d'inscription

Administrateur -> Systeme : Accéder à la page des demandes d'inscription
Systeme -> Systeme : Récupérer la liste des utilisateurs en attente
Systeme --> Administrateur : Afficher la liste des demandes

alt Approuver un utilisateur
    Administrateur -> Systeme : Cliquer sur "Approuver" pour un utilisateur
    Systeme --> Administrateur : Afficher une boîte de dialogue de confirmation
    
    Administrateur -> Systeme : Confirmer l'approbation
    
    Systeme -> Systeme : Marquer l'utilisateur comme approuvé
    Systeme -> Systeme : Envoyer un email de confirmation à l'utilisateur
    
    Systeme --> Administrateur : Afficher "Utilisateur approuvé avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des demandes
end

alt Rejeter un utilisateur
    Administrateur -> Systeme : Cliquer sur "Rejeter" pour un utilisateur
    Systeme --> Administrateur : Afficher une boîte de dialogue avec champ de raison
    
    Administrateur -> Systeme : Saisir la raison du rejet
    Administrateur -> Systeme : Confirmer le rejet
    
    Systeme -> Systeme : Envoyer un email de rejet à l'utilisateur avec la raison
    Systeme -> Systeme : Supprimer l'utilisateur de la base de données
    
    Systeme --> Administrateur : Afficher "Utilisateur rejeté avec succès"
    Systeme --> Administrateur : Mettre à jour la liste des demandes
end

@enduml
