<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Reclamation extends \App\Entity\Reclamation implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'date' => [parent::class, 'date', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        "\0".parent::class."\0".'message' => [parent::class, 'message', null, 16],
        "\0".parent::class."\0".'notifications' => [parent::class, 'notifications', null, 16],
        "\0".parent::class."\0".'response' => [parent::class, 'response', null, 16],
        "\0".parent::class."\0".'responseDate' => [parent::class, 'responseDate', null, 16],
        "\0".parent::class."\0".'responses' => [parent::class, 'responses', null, 16],
        "\0".parent::class."\0".'status' => [parent::class, 'status', null, 16],
        "\0".parent::class."\0".'subject' => [parent::class, 'subject', null, 16],
        "\0".parent::class."\0".'user' => [parent::class, 'user', null, 16],
        'date' => [parent::class, 'date', null, 16],
        'id' => [parent::class, 'id', null, 16],
        'message' => [parent::class, 'message', null, 16],
        'notifications' => [parent::class, 'notifications', null, 16],
        'response' => [parent::class, 'response', null, 16],
        'responseDate' => [parent::class, 'responseDate', null, 16],
        'responses' => [parent::class, 'responses', null, 16],
        'status' => [parent::class, 'status', null, 16],
        'subject' => [parent::class, 'subject', null, 16],
        'user' => [parent::class, 'user', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}
