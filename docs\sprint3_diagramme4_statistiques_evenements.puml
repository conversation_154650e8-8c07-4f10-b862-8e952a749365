@startuml Sprint 3 - Diagramme 4 : Statistiques des Évaluations et Gestion des Événements

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F
actor "Administrateur" as Admin
actor "Apprenant" as A
participant "Système" as S

== Statistiques des Évaluations et Gestion des Événements ==

group Statistiques des évaluations
    alt Formateur
        F -> S : Accéder aux statistiques
        S --> F : Afficher les statistiques de ses évaluations

        note right of S : Statistiques personnelles\ndu formateur uniquement

    else Administrateur
        Admin -> S : Accéder aux statistiques globales
        S --> Admin : Afficher toutes les statistiques d'évaluation

        note right of S : Vue d'ensemble de toutes\nles évaluations du système
    end
end

group Création d'événement d'évaluation
    Admin -> S : Accéder au calendrier des événements
    S --> Admin : Afficher le calendrier
    Admin -> S : C<PERSON>er un nouvel événement
    S --> Admin : Afficher le formulaire de création
    Admin -> S : Saisir les détails de l'événement
    Admin -> S : Valider la création
    S -> S : Enregistrer l'événement
    S -> A : Envoyer notification aux apprenants
    S -> F : Envoyer notification aux formateurs
    S --> Admin : Confirmer la création de l'événement

    note right of S : Notifications automatiques\nenvoyées à tous les utilisateurs concernés
end

@enduml
