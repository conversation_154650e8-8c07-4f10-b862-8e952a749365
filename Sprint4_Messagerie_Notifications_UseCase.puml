@startuml Sprint4_Messagerie_Notifications_UseCase

!define RECTANGLE class

skinparam backgroundColor #FAFAFA
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1976D2
    FontSize 12
    FontStyle bold
}

skinparam usecase {
    BackgroundColor #FFFFFF
    BorderColor #424242
    FontColor #212121
    FontSize 11
    BorderThickness 2
}

skinparam rectangle {
    BackgroundColor #F5F5F5
    BorderColor #757575
    FontColor #424242
    FontSize 12
    FontStyle bold
}

skinparam note {
    BackgroundColor #FFF3E0
    BorderColor #FF9800
    FontColor #E65100
    FontSize 10
}

skinparam arrow {
    Color #424242
    FontColor #424242
    FontSize 10
}

title Sprint 4 - Messagerie et Notifications\nDiagramme de Cas d'Usage

' Acteurs
actor "Formateur" as Formateur #lightblue
actor "Apprenant" as Apprenant #lightgreen
actor "Administrateur" as Admin #lightyellow

' Système
rectangle "Système PharmaLearn" as System {

    ' Authentification obligatoire
    usecase "S'authentifier\navec JWT" as UC0

    ' Package Messagerie
    rectangle "Messagerie" as MessageriePackage #E8F5E8 {
        usecase "Échanger des messages\navec les apprenants" as UC1
        usecase "Consulter l'historique\ndes conversations" as UC1b
    }

    ' Package Notifications
    rectangle "Notifications" as NotificationPackage #E3F2FD {
        usecase "Recevoir des notifications\npour évaluations" as UC2a
        usecase "Recevoir des notifications\npour certifications" as UC2b
        usecase "Recevoir des notifications\npour messages" as UC2c
        usecase "Consulter le centre\nde notifications" as UC2d
    }

    ' Package Réclamations
    rectangle "Réclamations" as ReclamationPackage #FFF3E0 {
        usecase "Envoyer des réclamations\naux administrateurs" as UC3
        usecase "Consulter ses\nréclamations" as UC3b
        usecase "Répondre aux\nréclamations" as UC4
        usecase "Gérer le statut\ndes réclamations" as UC4b
    }
}

' Authentification obligatoire pour tous
Formateur --> UC0 : "Connexion requise"
Apprenant --> UC0 : "Connexion requise"
Admin --> UC0 : "Connexion requise"

' Relations Formateur - Messagerie
Formateur --> UC1 : "Envoie des messages"
Formateur --> UC1b : "Consulte historique"

' Relations Apprenant - Messagerie
Apprenant --> UC1 : "Répond aux messages"
Apprenant --> UC1b : "Consulte historique"

' Relations Apprenant - Notifications
Apprenant --> UC2a : "Notifications évaluations"
Apprenant --> UC2b : "Notifications certifications"
Apprenant --> UC2c : "Notifications messages"
Apprenant --> UC2d : "Centre notifications"

' Relations Apprenant - Réclamations
Apprenant --> UC3 : "Envoie réclamations"
Apprenant --> UC3b : "Consulte ses réclamations"

' Relations Administrateur - Réclamations
Admin --> UC4 : "Répond aux réclamations"
Admin --> UC4b : "Gère les statuts"

' Relations d'inclusion - Authentification obligatoire
UC1 ..> UC0 : <<include>>\n"Authentification requise"
UC1b ..> UC0 : <<include>>\n"Authentification requise"
UC2a ..> UC0 : <<include>>\n"Authentification requise"
UC2b ..> UC0 : <<include>>\n"Authentification requise"
UC2c ..> UC0 : <<include>>\n"Authentification requise"
UC2d ..> UC0 : <<include>>\n"Authentification requise"
UC3 ..> UC0 : <<include>>\n"Authentification requise"
UC3b ..> UC0 : <<include>>\n"Authentification requise"
UC4 ..> UC0 : <<include>>\n"Authentification requise"
UC4b ..> UC0 : <<include>>\n"Authentification requise"

' Relations entre cas d'usage - Génération automatique de notifications
UC1 ..> UC2c : <<include>>\n"Génère notification\nmessage"
UC3 ..> UC2c : <<include>>\n"Génère notification\nréclamation"
UC4 ..> UC2c : <<include>>\n"Génère notification\nréponse"

@enduml
