<?php

namespace App\Entity;

use App\Repository\MessagerieRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: MessagerieRepository::class)]
class Messagerie
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['messagerie:read'])]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Groups(['messagerie:read'])]
    private ?string $message = null;

    #[ORM\Column]
    #[Groups(['messagerie:read'])]
    private ?bool $lu = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['messagerie:read'])]
    private ?\DateTimeInterface $date = null;

    #[ORM\ManyToOne(inversedBy: 'messenger')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['messagerie:read'])]
    private ?Formateur $formateur = null;

    #[ORM\ManyToOne(inversedBy: 'Messagerie')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['messagerie:read'])]
    private ?Apprenant $apprenant = null;

    /**
     * Indique si le message a été envoyé par le formateur (true) ou par l'apprenant (false)
     */
    #[ORM\Column(type: "boolean")]
    #[Groups(['messagerie:read'])]
    private bool $sentByFormateur = false;

    /**
     * @var Collection<int, Notification>
     */
    #[ORM\OneToMany(targetEntity: Notification::class, mappedBy: 'messagerie')]
    private Collection $notifications;

    public function __construct()
    {
        $this->notifications = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): static
    {
        $this->message = $message;

        return $this;
    }

    public function isLu(): ?bool
    {
        return $this->lu;
    }

    public function setLu(bool $lu): static
    {
        $this->lu = $lu;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): static
    {
        $this->date = $date;

        return $this;
    }

    public function getFormateur(): ?Formateur
    {
        return $this->formateur;
    }

    public function setFormateur(?Formateur $formateur): static
    {
        $this->formateur = $formateur;

        return $this;
    }

    public function getApprenant(): ?Apprenant
    {
        return $this->apprenant;
    }

    public function setApprenant(?Apprenant $apprenant): static
    {
        $this->apprenant = $apprenant;

        return $this;
    }

    public function isSentByFormateur(): bool
    {
        return $this->sentByFormateur;
    }

    public function setSentByFormateur(bool $sentByFormateur): static
    {
        $this->sentByFormateur = $sentByFormateur;

        return $this;
    }

    /**
     * @return Collection<int, Notification>
     */
    public function getNotifications(): Collection
    {
        return $this->notifications;
    }

    public function addNotification(Notification $notification): static
    {
        if (!$this->notifications->contains($notification)) {
            $this->notifications->add($notification);
            $notification->setMessagerie($this);
        }

        return $this;
    }

    public function removeNotification(Notification $notification): static
    {
        if ($this->notifications->removeElement($notification)) {
            // set the owning side to null (unless already changed)
            if ($notification->getMessagerie() === $this) {
                $notification->setMessagerie(null);
            }
        }

        return $this;
    }
}
