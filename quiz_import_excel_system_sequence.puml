@startuml Quiz Import Excel System Sequence

actor "Administrateur" as Admin
participant "Système" as System

title "Diagramme de séquence système : Import d'un quiz via Excel"

Admin -> System : Accéder à la page de gestion des cours
System --> Admin : Afficher la liste des cours

Admin -> System : Sélectionner un cours
System --> Admin : Afficher les détails du cours et ses quiz

Admin -> System : Cliquer sur "Importer un quiz via Excel"
System --> Admin : Afficher le formulaire d'import Excel

Admin -> System : Sélectionner le fichier Excel
Admin -> System : Cliquer sur "Importer"
System -> System : Analyser le fichier Excel

alt [Fichier valide]
    System -> System : Extraire et enregistrer les données (quiz, compétences, sous-compétences, actions)
    System --> Admin : Afficher un message de succès
    System --> Admin : Mettre à jour la liste des quiz du cours avec le quiz importé
else [Fichier invalide ou erreur d'importation]
    System --> Admin : Afficher les erreurs d'importation
end

@enduml
