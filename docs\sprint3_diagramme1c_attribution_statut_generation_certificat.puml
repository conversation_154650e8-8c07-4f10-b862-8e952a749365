@startuml Sprint 3 - Diagramme 1C : Attribution du Statut Final et Génération de Certificat

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Formateur" as F
actor "Apprenant" as A
participant "Système" as S

== Attribution du Statut Final et Génération de Certificat ==

group Attribution du statut final
    F -> S : Vérifier la complétude de l'évaluation
    S -> S : Vérifier que toutes les compétences sont évaluées
    S -> S : Vérifier que toutes les actions sont cochées

    note right of S : Condition obligatoire :\nToutes les actions doivent être cochées\npour pouvoir attribuer "Satisfaisant"

    alt Quiz Satisfaisant
        F -> S : Attribuer statut "Satisfaisant"
        S --> F : Confirmer l'attribution du statut
        S -> S : Enregistrer tous les détails d'évaluation
        S -> S : Mar<PERSON> le quiz comme "Satisfaisant"
        S -> A : Envoyer notification de statut "Satisfaisant"
        S -> S : Calculer la progression du cours

        alt Tous les quiz du cours sont "Satisfaisant"
            S -> S : Progression = 100%
            S -> S : Générer automatiquement le certificat
            S -> A : Envoyer notification de certificat disponible

            note right of S : Certificat généré uniquement\nquand TOUS les quiz du cours\nsont marqués "Satisfaisant"

        else Pas tous les quiz satisfaisants
            S -> S : Progression < 100%
            S -> S : Pas de génération de certificat

            note right of S : L'apprenant doit compléter\nles autres quiz du cours
        end

    else Quiz Non Satisfaisant
        F -> S : Attribuer statut "Non Satisfaisant"
        S --> F : Confirmer l'attribution du statut
        S -> S : Enregistrer les détails d'évaluation partiels
        S -> A : Envoyer notification de statut "Non Satisfaisant"

        note right of S : L'apprenant devra reprendre\nles éléments non satisfaisants
    end

    S -> S : Enregistrer l'évaluation complète
end

@enduml
