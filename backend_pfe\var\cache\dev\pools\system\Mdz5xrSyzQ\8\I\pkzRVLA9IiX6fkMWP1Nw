<?php //resource_metadata_collection_43a9bc28f1f70b463a0b667c72f2c02c

return [PHP_INT_MAX, static fn () => \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['ApiPlatform\\Metadata\\ErrorResource'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('ApiPlatform\\Metadata\\ErrorResource')),
        clone ($p['ApiPlatform\\Metadata\\Operations'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('ApiPlatform\\Metadata\\Operations')),
        clone ($p['ApiPlatform\\Metadata\\Error'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('ApiPlatform\\Metadata\\Error')),
        clone ($p['ApiPlatform\\Metadata\\Link'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('ApiPlatform\\Metadata\\Link')),
        clone $p['ApiPlatform\\Metadata\\Error'],
        clone $p['ApiPlatform\\Metadata\\Link'],
        clone ($p['Symfony\\Component\\WebLink\\Link'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Symfony\\Component\\WebLink\\Link')),
        clone $p['ApiPlatform\\Metadata\\Error'],
        clone $p['ApiPlatform\\Metadata\\Link'],
        clone $p['ApiPlatform\\Metadata\\Error'],
        clone $p['ApiPlatform\\Metadata\\Link'],
        clone $p['ApiPlatform\\Metadata\\Link'],
    ],
    null,
    [
        'ApiPlatform\\Metadata\\ApiResource' => [
            'shortName' => [
                'Error',
            ],
            'class' => [
                'ApiPlatform\\State\\ApiResource\\Error',
            ],
            'description' => [
                null,
            ],
            'urlGenerationStrategy' => [
                null,
            ],
            'deprecationReason' => [
                null,
            ],
            'normalizationContext' => [
                null,
            ],
            'denormalizationContext' => [
                null,
            ],
            'collectDenormalizationErrors' => [
                null,
            ],
            'validationContext' => [
                null,
            ],
            'filters' => [
                null,
            ],
            'elasticsearch' => [
                null,
            ],
            'order' => [
                null,
            ],
            'fetchPartial' => [
                null,
            ],
            'forceEager' => [
                null,
            ],
            'paginationEnabled' => [
                null,
            ],
            'paginationType' => [
                null,
            ],
            'paginationItemsPerPage' => [
                null,
            ],
            'paginationMaximumItemsPerPage' => [
                null,
            ],
            'paginationPartial' => [
                null,
            ],
            'paginationClientEnabled' => [
                null,
            ],
            'paginationClientItemsPerPage' => [
                null,
            ],
            'paginationClientPartial' => [
                null,
            ],
            'paginationFetchJoinCollection' => [
                null,
            ],
            'paginationUseOutputWalkers' => [
                null,
            ],
            'security' => [
                null,
            ],
            'securityMessage' => [
                null,
            ],
            'securityPostDenormalize' => [
                null,
            ],
            'securityPostDenormalizeMessage' => [
                null,
            ],
            'securityPostValidation' => [
                null,
            ],
            'securityPostValidationMessage' => [
                null,
            ],
            'stateOptions' => [
                null,
            ],
            'queryParameterValidationEnabled' => [
                null,
            ],
            'extraProperties' => [
                [
                    'standard_put' => true,
                    'rfc_7807_compliant_errors' => true,
                ],
            ],
            'operations' => [
                $o[1],
            ],
            'uriTemplate' => [
                '/errors/{status}',
            ],
            'types' => [
                null,
            ],
            'formats' => [
                null,
            ],
            'inputFormats' => [
                null,
            ],
            'outputFormats' => [
                null,
            ],
            'uriVariables' => [
                [
                    'status' => $o[11],
                ],
            ],
            'routePrefix' => [
                null,
            ],
            'defaults' => [
                null,
            ],
            'requirements' => [
                null,
            ],
            'options' => [
                null,
            ],
            'stateless' => [
                true,
            ],
            'sunset' => [
                null,
            ],
            'acceptPatch' => [
                null,
            ],
            'status' => [
                null,
            ],
            'host' => [
                null,
            ],
            'schemes' => [
                null,
            ],
            'condition' => [
                null,
            ],
            'controller' => [
                null,
            ],
            'headers' => [
                null,
            ],
            'cacheHeaders' => [
                [
                    'vary' => [
                        'Content-Type',
                        'Authorization',
                        'Origin',
                    ],
                ],
            ],
            'hydraContext' => [
                null,
            ],
            'openapiContext' => [
                null,
            ],
            'openapi' => [
                false,
            ],
            'paginationViaCursor' => [
                null,
            ],
            'compositeIdentifier' => [
                null,
            ],
            'exceptionToStatus' => [
                null,
            ],
            'links' => [
                null,
            ],
            'graphQlOperations' => [
                [],
            ],
        ],
        'ApiPlatform\\Metadata\\Metadata' => [
            'provider' => [
                'api_platform.state.error_provider',
            ],
            'queryParameterValidationEnabled' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
        ],
        'ApiPlatform\\Metadata\\Operations' => [
            'operations' => [
                1 => [
                    [
                        '_api_errors_problem',
                        $o[2],
                    ],
                    [
                        '_api_errors_hydra',
                        $o[4],
                    ],
                    [
                        '_api_errors_jsonapi',
                        $o[7],
                    ],
                    [
                        '_api_errors',
                        $o[9],
                    ],
                ],
            ],
        ],
        'ApiPlatform\\Metadata\\Operation' => [
            'shortName' => [
                2 => 'Error',
                4 => 'Error',
                7 => 'Error',
                9 => 'Error',
            ],
            'class' => [
                2 => 'ApiPlatform\\State\\ApiResource\\Error',
                4 => 'ApiPlatform\\State\\ApiResource\\Error',
                7 => 'ApiPlatform\\State\\ApiResource\\Error',
                9 => 'ApiPlatform\\State\\ApiResource\\Error',
            ],
            'description' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'urlGenerationStrategy' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'deprecationReason' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'normalizationContext' => [
                2 => [
                    'groups' => [
                        'jsonproblem',
                    ],
                    'skip_null_values' => true,
                    'rfc_7807_compliant_errors' => true,
                ],
                4 => [
                    'groups' => [
                        'jsonld',
                    ],
                    'skip_null_values' => true,
                    'rfc_7807_compliant_errors' => true,
                ],
                7 => [
                    'groups' => [
                        'jsonapi',
                    ],
                    'skip_null_values' => true,
                    'rfc_7807_compliant_errors' => true,
                ],
                9 => null,
            ],
            'denormalizationContext' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'collectDenormalizationErrors' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'validationContext' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'filters' => [
                2 => [],
                4 => [],
                7 => [],
                9 => [],
            ],
            'elasticsearch' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'order' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'fetchPartial' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'forceEager' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationEnabled' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationType' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationItemsPerPage' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationMaximumItemsPerPage' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationPartial' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationClientEnabled' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationClientItemsPerPage' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationClientPartial' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationFetchJoinCollection' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'paginationUseOutputWalkers' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'security' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'securityMessage' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'securityPostDenormalize' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'securityPostDenormalizeMessage' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'securityPostValidation' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'securityPostValidationMessage' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'provider' => [
                2 => 'api_platform.state.error_provider',
                4 => 'api_platform.state.error_provider',
                7 => 'api_platform.state.error_provider',
                9 => 'api_platform.state.error_provider',
            ],
            'stateOptions' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'extraProperties' => [
                2 => [
                    'standard_put' => true,
                    'rfc_7807_compliant_errors' => true,
                    'user_defined_uri_template' => true,
                ],
                4 => [
                    'standard_put' => true,
                    'rfc_7807_compliant_errors' => true,
                    'user_defined_uri_template' => true,
                ],
                7 => [
                    'standard_put' => true,
                    'rfc_7807_compliant_errors' => true,
                    'user_defined_uri_template' => true,
                ],
                9 => [
                    'standard_put' => true,
                    'rfc_7807_compliant_errors' => true,
                    'user_defined_uri_template' => true,
                ],
            ],
            'read' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'deserialize' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'validate' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'write' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'serialize' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'priority' => [
                2 => 0,
                4 => 1,
                7 => 2,
                9 => 3,
            ],
            'name' => [
                2 => '_api_errors_problem',
                4 => '_api_errors_hydra',
                7 => '_api_errors_jsonapi',
                9 => '_api_errors',
            ],
        ],
        'ApiPlatform\\Metadata\\HttpOperation' => [
            'paginationViaCursor' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'method' => [
                2 => 'GET',
                4 => 'GET',
                7 => 'GET',
                9 => 'GET',
            ],
            'uriTemplate' => [
                2 => '/errors/{status}',
                4 => '/errors/{status}',
                7 => '/errors/{status}',
                9 => '/errors/{status}',
            ],
            'types' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'inputFormats' => [
                2 => [
                    'jsonld' => [
                        'application/ld+json',
                    ],
                ],
                4 => [
                    'jsonld' => [
                        'application/ld+json',
                    ],
                ],
                7 => [
                    'jsonld' => [
                        'application/ld+json',
                    ],
                ],
                9 => [
                    'jsonld' => [
                        'application/ld+json',
                    ],
                ],
            ],
            'outputFormats' => [
                2 => [
                    'json' => [
                        'application/problem+json',
                    ],
                ],
                4 => [
                    'jsonld' => [
                        'application/problem+json',
                    ],
                ],
                7 => [
                    'jsonapi' => [
                        'application/vnd.api+json',
                    ],
                ],
                9 => [
                    'jsonld' => [
                        'application/ld+json',
                    ],
                    'jsonproblem' => [
                        'application/problem+json',
                    ],
                    'json' => [
                        'application/problem+json',
                        'application/json',
                    ],
                ],
            ],
            'uriVariables' => [
                2 => [
                    'status' => $o[3],
                ],
                4 => [
                    'status' => $o[5],
                ],
                7 => [
                    'status' => $o[8],
                ],
                9 => [
                    'status' => $o[10],
                ],
            ],
            'routePrefix' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'routeName' => [
                2 => 'api_errors',
                4 => 'api_errors',
                7 => 'api_errors',
                9 => 'api_errors',
            ],
            'defaults' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'requirements' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'options' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'stateless' => [
                2 => true,
                4 => true,
                7 => true,
                9 => true,
            ],
            'sunset' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'acceptPatch' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'host' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'schemes' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'condition' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'controller' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'headers' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'cacheHeaders' => [
                2 => [
                    'vary' => [
                        'Content-Type',
                        'Authorization',
                        'Origin',
                    ],
                ],
                4 => [
                    'vary' => [
                        'Content-Type',
                        'Authorization',
                        'Origin',
                    ],
                ],
                7 => [
                    'vary' => [
                        'Content-Type',
                        'Authorization',
                        'Origin',
                    ],
                ],
                9 => [
                    'vary' => [
                        'Content-Type',
                        'Authorization',
                        'Origin',
                    ],
                ],
            ],
            'hydraContext' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'openapiContext' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'openapi' => [
                2 => false,
                4 => false,
                7 => false,
                9 => false,
            ],
            'exceptionToStatus' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
            'links' => [
                2 => null,
                4 => [
                    $o[6],
                ],
                7 => null,
                9 => null,
            ],
            'errors' => [
                2 => null,
                4 => null,
                7 => null,
                9 => null,
            ],
        ],
        'ApiPlatform\\Metadata\\Parameter' => [
            'key' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'schema' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'openApi' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'provider' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'filter' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'property' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'description' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'required' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'priority' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'hydra' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'constraints' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'security' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'securityMessage' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'extraProperties' => [
                3 => [],
                5 => [],
                8 => [],
                10 => [],
                [],
            ],
        ],
        'ApiPlatform\\Metadata\\Link' => [
            'parameterName' => [
                3 => 'status',
                5 => 'status',
                8 => 'status',
                10 => 'status',
                'status',
            ],
            'fromProperty' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'toProperty' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'fromClass' => [
                3 => 'ApiPlatform\\State\\ApiResource\\Error',
                5 => 'ApiPlatform\\State\\ApiResource\\Error',
                8 => 'ApiPlatform\\State\\ApiResource\\Error',
                10 => 'ApiPlatform\\State\\ApiResource\\Error',
                'ApiPlatform\\State\\ApiResource\\Error',
            ],
            'toClass' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'identifiers' => [
                3 => [
                    'status',
                ],
                5 => [
                    'status',
                ],
                8 => [
                    'status',
                ],
                10 => [
                    'status',
                ],
                [
                    'status',
                ],
            ],
            'compositeIdentifier' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'expandedValue' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
            'securityObjectName' => [
                3 => null,
                5 => null,
                8 => null,
                10 => null,
                null,
            ],
        ],
        'Symfony\\Component\\WebLink\\Link' => [
            'href' => [
                6 => 'http://www.w3.org/ns/hydra/error',
            ],
            'rel' => [
                6 => [
                    'http://www.w3.org/ns/json-ld#error' => 'http://www.w3.org/ns/json-ld#error',
                ],
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
)];
