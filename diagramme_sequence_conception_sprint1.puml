@startuml Diagramme de Séquence de Conception - Sprint 1 (Authentification)

actor Utilisateur
participant "<<View>>\nRegister.jsx" as RegisterView
participant "<<ViewModel>>\nAuthContext" as AuthContext
participant "<<Model>>\nAuthController" as AuthController
participant "<<Model>>\nUtilisateur" as UtilisateurModel

title Diagramme de Séquence de Conception - Sprint 1 (Authentification)

ref over Utilisateur, UtilisateurModel : S'authentifier

== Inscription ==

Utilisateur -> RegisterView : Cliquer sur "S'inscrire"
activate RegisterView

RegisterView -> RegisterView : Valider les données du formulaire
RegisterView -> AuthContext : register(userData)
activate AuthContext

AuthContext -> AuthController : POST /api/register
activate AuthController

AuthController -> UtilisateurModel : create(userData)
activate UtilisateurModel
UtilisateurModel --> AuthController : utilisateur
deactivate UtilisateurModel

AuthController -> AuthController : validate(utilisateur)
AuthController -> AuthController : persist(utilisateur)
AuthController --> AuthContext : response
deactivate AuthController

alt errors.length==0
    AuthContext --> RegisterView : {success: true, user: data}
    RegisterView --> Utilisateur : afficher "Inscription réussie. Votre compte est en attente d'approbation."
else
    AuthContext --> RegisterView : {success: false, error: messages}
    RegisterView --> Utilisateur : afficher les erreurs
end

deactivate AuthContext
deactivate RegisterView

== Connexion ==

Utilisateur -> "<<View>>\nLogin.jsx" as LoginView : Cliquer sur "Se connecter"
activate LoginView

LoginView -> LoginView : Valider les données du formulaire
LoginView -> AuthContext : login(email, password)
activate AuthContext

AuthContext -> AuthController : POST /api/login
activate AuthController

AuthController -> UtilisateurModel : findByEmail(email)
activate UtilisateurModel
UtilisateurModel --> AuthController : utilisateur
deactivate UtilisateurModel

AuthController -> AuthController : verifyPassword(password)
AuthController -> AuthController : generateJwtToken(utilisateur)
AuthController --> AuthContext : response
deactivate AuthController

alt errors.length==0
    AuthContext --> LoginView : {success: true, user: data, token: token}
    LoginView --> Utilisateur : rediriger vers le tableau de bord
else
    AuthContext --> LoginView : {success: false, error: message}
    LoginView --> Utilisateur : afficher les erreurs
end

deactivate AuthContext
deactivate LoginView

== Réinitialisation de mot de passe ==

Utilisateur -> "<<View>>\nForgotPassword.jsx" as ForgotPwdView : Cliquer sur "Mot de passe oublié"
activate ForgotPwdView

ForgotPwdView -> ForgotPwdView : Valider l'email
ForgotPwdView -> AuthContext : forgotPassword(email)
activate AuthContext

AuthContext -> AuthController : POST /api/forgot-password
activate AuthController

AuthController -> UtilisateurModel : findByEmail(email)
activate UtilisateurModel
UtilisateurModel --> AuthController : utilisateur
deactivate UtilisateurModel

AuthController -> AuthController : generateResetToken()
AuthController -> AuthController : sendResetEmail(utilisateur, token)
AuthController --> AuthContext : response
deactivate AuthController

AuthContext --> ForgotPwdView : {success: true}
ForgotPwdView --> Utilisateur : afficher "Un email a été envoyé si l'adresse existe"

deactivate AuthContext
deactivate ForgotPwdView

@enduml
