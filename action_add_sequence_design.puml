@startuml Action Add Sequence Design

title Diagramme de Séquence - Ajout d'une action

actor Utilisateur
participant "«View»\nActionForm.jsx" as View
participant "«Controller»\nActionContext" as Context
participant "«Controller»\nActionController" as Controller
participant "«Model»\nAction" as Model

ref over Utilisateur, Model
  Ajouter une action
end ref

Utilisateur -> View : Cliquer sur "Ajouter une action"
View --> Utilisateur : Afficher le formulaire d'ajout d'action

Utilisateur -> View : Remplir le formulaire (nom_fr, nom_en, categorie_fr, categorie_en)
Utilisateur -> View : Cliquer sur "Enregistrer"
View -> View : Valider les données du formulaire
View -> Context : addAction(actionData)

Context -> Controller : POST /api/action
Controller -> Model : Valider les données
Model -> Model : create(actionData)
Model -> Model : persist(action)
Model --> Controller : response

alt [success: true]
    Controller --> Context : {success: true, action: data}
    Context --> View : Mettre à jour la liste des actions
    View --> Utilisateur : Afficher "Action ajoutée avec succès"
else [success: false]
    Controller --> Context : {success: false, error: messages}
    Context --> View : Transmettre les erreurs
    View --> Utilisateur : Afficher les erreurs
end

@enduml
