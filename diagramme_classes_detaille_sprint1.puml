@startuml Diagramme de Classes Détaillé - Sprint 1

skinparam classAttributeIconSize 0
skinparam classFontStyle bold
skinparam classFontSize 14
skinparam classBackgroundColor white
skinparam classBorderColor black
skinparam classBorderThickness 2
skinparam classStereotypeFontSize 12
skinparam circledCharacterFontSize 12
skinparam circledCharacterRadius 12
skinparam circledCharacterFontName Arial
skinparam defaultFontName Arial
skinparam arrowThickness 2
skinparam arrowColor black

' Vues Frontend
class RegisterView {
  -formData: Object
  -formError: String
  -successMessage: String
  -imagePreview: String
  -showPassword: Boolean
  -showConfirmPassword: Boolean
  -loading: Boolean
  +handleSubmit(): void
  +handleChange(): void
  +handleImageUpload(): void
  +togglePasswordVisibility(): void
}

class LoginView {
  -email: String
  -password: String
  -formError: String
  -rememberMe: Boolean
  -showPassword: Boolean
  -loading: Boolean
  +handleSubmit(): void
  +handleChange(): void
  +togglePasswordVisibility(): void
}

class ForgotPasswordView {
  -email: String
  -formError: String
  -successMessage: String
  -loading: Boolean
  +handleSubmit(): void
  +handleChange(): void
}

class ResetPasswordView {
  -password: String
  -confirmPassword: String
  -formError: String
  -successMessage: String
  -resetComplete: Boolean
  -showPassword: Boolean
  -loading: Boolean
  +handleSubmit(): void
  +handleChange(): void
  +togglePasswordVisibility(): void
}

class UserManagementView {
  -users: Array
  -searchTerm: String
  -roleFilter: String
  -currentPage: Number
  -loading: Boolean
  -dialog: Object
  +getUsers(): void
  +handleSearch(): void
  +handleFilter(): void
  +handlePageChange(): void
  +handleAddUser(): void
  +handleEditUser(): void
  +handleDeleteUser(): void
  +handleRoleChange(): void
}

class RequestsView {
  -requests: Array
  -searchTerm: String
  -statusFilter: String
  -currentPage: Number
  -loading: Boolean
  -dialog: Object
  +getPendingUsers(): void
  +handleSearch(): void
  +handleFilter(): void
  +handlePageChange(): void
  +handleApproveUser(): void
  +handleRejectUser(): void
}

' Contrôleurs Backend
class AuthController {
  -entityManager: EntityManagerInterface
  -passwordHasher: UserPasswordHasherInterface
  -validator: ValidatorInterface
  -utilisateurRepository: UtilisateurRepository
  -administrateurRepository: AdministrateurRepository
  -jwtSecret: String
  +register(request: Request): JsonResponse
  +login(request: Request): JsonResponse
  +logout(): JsonResponse
  +getCurrentUser(): JsonResponse
  +updateUser(id: int, request: Request): JsonResponse
  +forgotPassword(request: Request): JsonResponse
  +resetPassword(request: Request, token: String): JsonResponse
  -generateJwtToken(user: Utilisateur): String
}

class AdminController {
  -entityManager: EntityManagerInterface
  -utilisateurRepository: UtilisateurRepository
  -emailService: EmailService
  +getApprovedUsers(): JsonResponse
  +getPendingUsers(): JsonResponse
  +approveUser(id: int, request: Request): JsonResponse
  +rejectUser(id: int, request: Request): JsonResponse
  +editUser(id: int, request: Request): JsonResponse
  +deleteUser(id: int): JsonResponse
}

' Modèles et Services
class Utilisateur {
  -id: int
  -name: String
  -email: String
  -phone: int
  -profileImage: String
  -password: String
  -role: String
  -roles: Array
  -isApproved: Boolean
  -resetToken: String
  -resetTokenExpiresAt: DateTime
  +getId(): int
  +getName(): String
  +setName(name: String): self
  +getEmail(): String
  +setEmail(email: String): self
  +getPhone(): int
  +setPhone(phone: int): self
  +getProfileImage(): String
  +setProfileImage(profileImage: String): self
  +getPassword(): String
  +setPassword(password: String): self
  +getRole(): String
  +setRole(role: String): self
  +getRoles(): Array
  +setRoles(roles: Array): self
  +isApproved(): Boolean
  +setIsApproved(isApproved: Boolean): self
  +getResetToken(): String
  +setResetToken(resetToken: String): self
  +getResetTokenExpiresAt(): DateTime
  +setResetTokenExpiresAt(date: DateTime): self
}

class EmailService {
  -mailer: MailerInterface
  -router: RouterInterface
  -twig: Environment
  -senderEmail: String
  +sendApprovalEmail(user: Utilisateur): Boolean
  +sendRejectionEmail(user: Utilisateur, reason: String): Boolean
  +sendPasswordResetEmail(user: Utilisateur, resetToken: String): Boolean
}

class JwtAuthenticator {
  -jwtSecret: String
  -utilisateurRepository: UtilisateurRepository
  +supports(request: Request): Boolean
  +authenticate(request: Request): Passport
  +onAuthenticationSuccess(request: Request, token: TokenInterface, firewallName: String): Response
  +onAuthenticationFailure(request: Request, exception: AuthenticationException): Response
  +start(request: Request, authException: AuthenticationException): Response
}

class AuthContext {
  -user: Object
  -token: String
  -loading: Boolean
  -error: String
  +login(email: String, password: String, rememberMe: Boolean): Promise
  +register(userData: Object): Promise
  +logout(): void
  +updateProfile(userData: Object): Promise
  +deleteAccount(): Promise
  +getPendingUsers(): Promise
  +approveUser(userId: Number, role: String): Promise
  +rejectUser(userId: Number, reason: String): Promise
  +forgotPassword(email: String): Promise
  +resetPassword(token: String, password: String): Promise
}

' Relations
RegisterView --> AuthContext
LoginView --> AuthContext
ForgotPasswordView --> AuthContext
ResetPasswordView --> AuthContext
UserManagementView --> AuthContext
RequestsView --> AuthContext

AuthContext --> AuthController : HTTP Requests
AuthContext --> AdminController : HTTP Requests

AuthController --> Utilisateur : manages
AdminController --> Utilisateur : manages
AuthController --> JwtAuthenticator : uses
AdminController --> EmailService : uses

@enduml
