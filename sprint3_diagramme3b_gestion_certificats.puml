@startuml Sprint 3 - Diagramme 3B : Gestion des Certificats par l'Administrateur - Architecture MVC

!define RECTANGLE class

skinparam participant {
    BackgroundColor white
    BorderColor black
    FontSize 12
}

skinparam arrow {
    Color black
    Thickness 1
}

skinparam note {
    BackgroundColor lightyellow
    BorderColor black
}

actor "Administrateur" as Admin

participant "«View»\nCourseManagementView" as CMV

participant "«Controller»\nCertificatController" as CC

participant "«Model»\nCertificat" as CM
participant "«Model»\nApprenant" as AM
participant "«Model»\nCours" as CoM
participant "«Model»\nProgression" as PM

== Consultation des certificats délivrés par l'administrateur ==

Admin -> CMV : Accéder à la gestion des certificats
CMV -> CC : list()
CC -> CM : findAll()
CM --> CC : certificats[]
CC --> CMV : JsonResponse avec liste complète des certificats
CMV --> Admin : afficher liste des certificats délivrés

note right of CMV : Vue d'ensemble de tous\nles certificats délivrés

Admin -> CMV : Filtrer par apprenant
CMV -> CC : getCertificatsByApprenant(apprenantId)
CC -> CM : findBy(['apprenant' => apprenantId])
CM --> CC : certificats filtrés
CC --> CMV : JsonResponse avec certificats de l'apprenant
CMV --> Admin : afficher certificats filtrés

Admin -> CMV : Filtrer par cours
CMV -> CC : getCertificatsByCours(coursId)
CC -> PM : findBy(['cours' => coursId])
PM --> CC : progressions[]
CC -> CM : findBy(['progression' => progressions])
CM --> CC : certificats filtrés
CC --> CMV : JsonResponse avec certificats du cours
CMV --> Admin : afficher certificats filtrés

Admin -> CMV : Consulter détails d'un certificat
CMV -> CC : show(certificatId)
CC -> CM : find(certificatId)
CM --> CC : certificat
CC -> AM : find(certificat.apprenant.id)
AM --> CC : apprenant
CC -> PM : find(certificat.progression.id)
PM --> CC : progression
CC -> CoM : find(progression.cours.id)
CoM --> CC : cours
CC --> CMV : JsonResponse avec détails complets du certificat
CMV --> Admin : afficher informations détaillées

note right of CMV : Détails complets :\n- Informations apprenant\n- Cours associé\n- Date d'obtention\n- Progression

@enduml
